# 数学计算 MCP 服务器
# 实现基础数学运算：加法、减法、乘法、除法等功能
from mcp.server.fastmcp import FastMCP
from typing import Union
import math

# 创建 MCP 服务
mcp = FastMCP('math_calculator')

@mcp.tool()
def add(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """
    计算两个数的和 (a + b = c)
    
    Args:
        a: 第一个数字
        b: 第二个数字
    
    Returns:
        两个数的和
    """
    result = a + b
    print(f"[MCP Math Tool] 加法运算: {a} + {b} = {result}")
    return result

@mcp.tool()
def subtract(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """
    计算两个数的差 (a - b = c)
    
    Args:
        a: 被减数
        b: 减数
    
    Returns:
        两个数的差
    """
    result = a - b
    print(f"[MCP Math Tool] 减法运算: {a} - {b} = {result}")
    return result

@mcp.tool()
def multiply(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """
    计算两个数的积 (a × b = c)
    
    Args:
        a: 第一个数字
        b: 第二个数字
    
    Returns:
        两个数的积
    """
    result = a * b
    print(f"[MCP Math Tool] 乘法运算: {a} × {b} = {result}")
    return result

@mcp.tool()
def divide(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """
    计算两个数的商 (a ÷ b = c)
    
    Args:
        a: 被除数
        b: 除数
    
    Returns:
        两个数的商
    
    Raises:
        ValueError: 当除数为0时抛出异常
    """
    if b == 0:
        raise ValueError("除数不能为0")
    
    result = a / b
    print(f"[MCP Math Tool] 除法运算: {a} ÷ {b} = {result}")
    return result

@mcp.tool()
def power(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """
    计算a的b次方 (a^b = c)
    
    Args:
        a: 底数
        b: 指数
    
    Returns:
        a的b次方
    """
    result = a ** b
    print(f"[MCP Math Tool] 幂运算: {a}^{b} = {result}")
    return result

@mcp.tool()
def square_root(a: Union[int, float]) -> float:
    """
    计算数字的平方根 (√a = c)
    
    Args:
        a: 要计算平方根的数字
    
    Returns:
        数字的平方根
    
    Raises:
        ValueError: 当输入负数时抛出异常
    """
    if a < 0:
        raise ValueError("不能计算负数的平方根")
    
    result = math.sqrt(a)
    print(f"[MCP Math Tool] 平方根运算: √{a} = {result}")
    return result

@mcp.tool()
def percentage(a: Union[int, float], b: Union[int, float]) -> float:
    """
    计算a占b的百分比 (a/b × 100% = c%)
    
    Args:
        a: 部分数值
        b: 总数值
    
    Returns:
        百分比值
    
    Raises:
        ValueError: 当总数值为0时抛出异常
    """
    if b == 0:
        raise ValueError("总数值不能为0")
    
    result = (a / b) * 100
    print(f"[MCP Math Tool] 百分比计算: {a}/{b} × 100% = {result}%")
    return result

@mcp.tool()
def factorial(n: int) -> int:
    """
    计算n的阶乘 (n! = c)
    
    Args:
        n: 要计算阶乘的非负整数
    
    Returns:
        n的阶乘
    
    Raises:
        ValueError: 当输入负数时抛出异常
    """
    if n < 0:
        raise ValueError("不能计算负数的阶乘")
    
    result = math.factorial(n)
    print(f"[MCP Math Tool] 阶乘运算: {n}! = {result}")
    return result

@mcp.tool()
def solve_linear_equation(a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
    """
    解一元一次方程 ax + b = 0，求x的值
    
    Args:
        a: x的系数
        b: 常数项
    
    Returns:
        方程的解x
    
    Raises:
        ValueError: 当a为0时抛出异常（不是一次方程）
    """
    if a == 0:
        raise ValueError("系数a不能为0，这不是一次方程")
    
    result = -b / a
    print(f"[MCP Math Tool] 一元一次方程求解: {a}x + {b} = 0, x = {result}")
    return result

@mcp.tool()
def calculate_expression(expression: str) -> Union[int, float]:
    """
    计算数学表达式的值
    
    Args:
        expression: 数学表达式字符串（如 "2 + 3 * 4"）
    
    Returns:
        表达式的计算结果
    
    Raises:
        ValueError: 当表达式无效时抛出异常
    """
    try:
        # 为了安全，只允许基本的数学运算
        allowed_chars = set('0123456789+-*/()., ')
        if not all(c in allowed_chars for c in expression):
            raise ValueError("表达式包含不允许的字符")
        
        result = eval(expression)
        print(f"[MCP Math Tool] 表达式计算: {expression} = {result}")
        return result
    except Exception as e:
        raise ValueError(f"无效的数学表达式: {str(e)}")

if __name__ == "__main__":
    # 以标准 I/O 方式运行 MCP 服务器
    print("启动数学计算 MCP 服务器...")
    mcp.run(transport='stdio')
