{"cells": [{"attachments": {"b77a7d3b-b28a-4dcf-9f1a-861f2f2c5f6c.png": {"image/png": "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******************************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"}}, "cell_type": "markdown", "id": "92ddc4f4-f7bf-4e0e-b5a5-5abd8a008b21", "metadata": {}, "source": ["# Corrective RAG (CRAG) using local LLMs\n", "\n", "[Corrective-RAG (CRAG)](https://arxiv.org/abs/2401.15884) is a strategy for RAG that incorporates self-reflection / self-grading on retrieved documents. \n", "\n", "The paper follows this general flow:\n", "\n", "* If at least one document exceeds the threshold for `relevance`, then it proceeds to generation\n", "* If all documents fall below the `relevance` threshold or if the grader is unsure, then it uses web search to supplement retrieval\n", "* Before generation, it performs knowledge refinement of the search or retrieved documents\n", "* This partitions the document into `knowledge strips`\n", "* It grades each strip, and filters out irrelevant ones\n", "\n", "We will implement some of these ideas from scratch using [LangGraph](https://langchain-ai.github.io/langgraph/):\n", "\n", "* If *any* documents are irrelevant, we'll supplement retrieval with web search. \n", "* We'll skip the knowledge refinement, but this can be added back as a node if desired. \n", "* We'll use [Tavily Search](https://python.langchain.com/docs/integrations/tools/tavily_search/) for web search.\n", "\n", "![Screenshot 2024-06-24 at 3.03.16 PM.png](attachment:b77a7d3b-b28a-4dcf-9f1a-861f2f2c5f6c.png)"]}, {"cell_type": "markdown", "id": "6ba4302f-09d9-4d2a-a18d-a6fd23704850", "metadata": {}, "source": ["## Setup\n", "\n", "We'll use [<PERSON>llama](https://ollama.ai/) to access a local LLM:\n", "\n", "* Download [Ollama app](https://ollama.ai/).\n", "* Pull your model of choice, e.g.: `ollama pull llama3`\n", "\n", "We'll use [<PERSON><PERSON>](https://python.langchain.com/docs/integrations/tools/tavily_search/) for web search.\n", "\n", "We'll use a vectorstore with [Nomic local embeddings](https://blog.nomic.ai/posts/nomic-embed-text-v1) or, optionally, OpenAI embeddings.\n", "\n", "\n", "Let's install our required packages and set our API keys:"]}, {"cell_type": "code", "execution_count": null, "id": "4a660963-bd3d-4c87-b2e4-b6e432055211", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langchain_community tiktoken langchainhub scikit-learn langchain langgraph tavily-python  nomic[local] langchain-nomic langchain_openai"]}, {"cell_type": "code", "execution_count": null, "id": "68316ba0-854b-41e1-9af5-1f9e965946e3", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(key: str):\n", "    if key not in os.environ:\n", "        os.environ[key] = getpass.getpass(f\"{key}:\")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")\n", "_set_env(\"TAVILY_API_KEY\")"]}, {"cell_type": "markdown", "id": "98f863ea", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "c059c3a3-7f01-4d46-8289-fde4c1b4155f", "metadata": {}, "source": ["### LLM\n", "\n", "You can select from [Ollama LLMs](https://ollama.com/library)."]}, {"cell_type": "code", "execution_count": 1, "id": "2f4db331-c4d0-4c7c-a9a5-0bebc8a89c6c", "metadata": {}, "outputs": [], "source": ["local_llm = \"llama3\"\n", "model_tested = \"llama3-8b\"\n", "metadata = f\"CRAG, {model_tested}\""]}, {"cell_type": "markdown", "id": "6e2b6eed-3b3f-44b5-a34a-4ade1e94caf0", "metadata": {}, "source": ["## Create Index\n", "\n", "Let's index 3 blog posts."]}, {"cell_type": "code", "execution_count": 3, "id": "bb8b789b-475b-4e1b-9c66-03504c837830", "metadata": {}, "outputs": [], "source": ["from langchain.text_splitter import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import WebBaseLoader\n", "from langchain_community.vectorstores import SKLearnVectorStore\n", "from langchain_nomic.embeddings import NomicEmbeddings  # local\n", "from langchain_openai import OpenAIEmbeddings  # api\n", "\n", "# List of URLs to load documents from\n", "urls = [\n", "    \"https://lilianweng.github.io/posts/2023-06-23-agent/\",\n", "    \"https://lilianweng.github.io/posts/2023-03-15-prompt-engineering/\",\n", "    \"https://lilianweng.github.io/posts/2023-10-25-adv-attack-llm/\",\n", "]\n", "\n", "# Load documents from the URLs\n", "docs = [WebBaseLoader(url).load() for url in urls]\n", "docs_list = [item for sublist in docs for item in sublist]\n", "\n", "# Initialize a text splitter with specified chunk size and overlap\n", "text_splitter = RecursiveCharacterTextSplitter.from_tiktoken_encoder(\n", "    chunk_size=250, chunk_overlap=0\n", ")\n", "\n", "# Split the documents into chunks\n", "doc_splits = text_splitter.split_documents(docs_list)\n", "\n", "# Embedding\n", "\"\"\"\n", "embedding=NomicEmbeddings(\n", "    model=\"nomic-embed-text-v1.5\",\n", "    inference_mode=\"local\",\n", ")\n", "\"\"\"\n", "embedding = OpenAIEmbeddings()\n", "\n", "# Add the document chunks to the \"vector store\"\n", "vectorstore = SKLearnVectorStore.from_documents(\n", "    documents=doc_splits,\n", "    embedding=embedding,\n", ")\n", "retriever = vectorstore.as_retriever(k=4)"]}, {"attachments": {}, "cell_type": "markdown", "id": "fe7fd10a-f64a-48de-a116-6d5890def1af", "metadata": {}, "source": ["## Define <PERSON>ls"]}, {"cell_type": "code", "execution_count": 4, "id": "0e75c029-6c10-47c7-871c-1f4932b25309", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'score': 1}\n"]}], "source": ["### Retrieval Grader\n", "\n", "from langchain.prompts import PromptTemplate\n", "from langchain_community.chat_models import ChatOllama\n", "from langchain_core.output_parsers import JsonOutputParser\n", "from langchain_mistralai.chat_models import ChatMistralAI\n", "\n", "# LLM\n", "llm = ChatOllama(model=local_llm, format=\"json\", temperature=0)\n", "\n", "# Prompt\n", "prompt = PromptTemplate(\n", "    template=\"\"\"You are a teacher grading a quiz. You will be given: \n", "    1/ a QUESTION\n", "    2/ A FACT provided by the student\n", "    \n", "    You are grading RELEVANCE RECALL:\n", "    A score of 1 means that ANY of the statements in the FACT are relevant to the QUESTION. \n", "    A score of 0 means that NONE of the statements in the FACT are relevant to the QUESTION. \n", "    1 is the highest (best) score. 0 is the lowest score you can give. \n", "    \n", "    Explain your reasoning in a step-by-step manner. Ensure your reasoning and conclusion are correct. \n", "    \n", "    Avoid simply stating the correct answer at the outset.\n", "    \n", "    Question: {question} \\n\n", "    Fact: \\n\\n {documents} \\n\\n\n", "    \n", "    Give a binary score 'yes' or 'no' score to indicate whether the document is relevant to the question. \\n\n", "    Provide the binary score as a JSON with a single key 'score' and no premable or explanation.\n", "    \"\"\",\n", "    input_variables=[\"question\", \"documents\"],\n", ")\n", "\n", "retrieval_grader = prompt | llm | JsonOutputParser()\n", "question = \"agent memory\"\n", "docs = retriever.invoke(question)\n", "doc_txt = docs[1].page_content\n", "print(retrieval_grader.invoke({\"question\": question, \"documents\": doc_txt}))"]}, {"cell_type": "code", "execution_count": 4, "id": "dad03302-bd93-43fc-949e-af51a3298cfa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The document mentions \"memory stream\" which is a long-term memory module that records a comprehensive list of agents' experience in natural language. It also discusses short-term memory and long-term memory, with the latter providing the agent with the capability to retain and recall information over extended periods. Additionally, it mentions planning and reflection mechanisms that enable agents to behave conditioned on past experience.\n"]}], "source": ["### Generate\n", "\n", "from langchain_core.output_parsers import StrOutputParser\n", "\n", "# Prompt\n", "prompt = PromptTemplate(\n", "    template=\"\"\"You are an assistant for question-answering tasks. \n", "    \n", "    Use the following documents to answer the question. \n", "    \n", "    If you don't know the answer, just say that you don't know. \n", "    \n", "    Use three sentences maximum and keep the answer concise:\n", "    Question: {question} \n", "    Documents: {documents} \n", "    Answer: \n", "    \"\"\",\n", "    input_variables=[\"question\", \"documents\"],\n", ")\n", "\n", "# LLM\n", "llm = ChatOllama(model=local_llm, temperature=0)\n", "\n", "# Chain\n", "rag_chain = prompt | llm | StrOutputParser()\n", "\n", "# Run\n", "generation = rag_chain.invoke({\"documents\": docs, \"question\": question})\n", "print(generation)"]}, {"cell_type": "code", "execution_count": 5, "id": "b36a2f36-bc5f-408d-a5e8-3fa203c233f6", "metadata": {}, "outputs": [], "source": ["### Search\n", "\n", "from langchain_community.tools.tavily_search import TavilySearchResults\n", "\n", "web_search_tool = TavilySearchResults(k=3)"]}, {"cell_type": "markdown", "id": "a3421cf0-9067-43fe-8681-0d3189d15dd3", "metadata": {}, "source": ["## Create the Graph \n", "\n", "Here we'll explicitly define the majority of the control flow, only using an LLM to define a single branch point following grading."]}, {"cell_type": "code", "execution_count": 6, "id": "10028794-2fbc-43f9-aa4c-7fe3abd69c1e", "metadata": {}, "outputs": [{"data": {"image/jpeg": "/9j/4AAQSkZJRgABAQAAAQABAAD/4gHYSUNDX1BST0ZJTEUAAQEAAAHIAAAAAAQwAABtbnRyUkdCIFhZWiAH4AABAAEAAAAAAABhY3NwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAA9tYAAQAAAADTLQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAlkZXNjAAAA8AAAACRyWFlaAAABFAAAABRnWFlaAAABKAAAABRiWFlaAAABPAAAABR3dHB0AAABUAAAABRyVFJDAAABZAAAAChnVFJDAAABZAAAAChiVFJDAAABZAAAAChjcHJ0AAABjAAAADxtbHVjAAAAAAAAAAEAAAAMZW5VUwAAAAgAAAAcAHMAUgBHAEJYWVogAAAAAAAAb6IAADj1AAADkFhZWiAAAAAAAABimQAAt4UAABjaWFlaIAAAAAAAACSgAAAPhAAAts9YWVogAAAAAAAA9tYAAQAAAADTLXBhcmEAAAAAAAQAAAACZmYAAPKnAAANWQAAE9AAAApbAAAAAAAAAABtbHVjAAAAAAAAAAEAAAAMZW5VUwAAACAAAAAcAEcAbwBvAGcAbABlACAASQBuAGMALgAgADIAMAAxADb/2wBDAAMCAgMCAgMDAwMEAwMEBQgFBQQEBQoHBwYIDAoMDAsKCwsNDhIQDQ4RDgsLEBYQERMUFRUVDA8XGBYUGBIUFRT/2wBDAQMEBAUEBQkFBQkUDQsNFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBQUFBT/wAARCAIrAMYDASIAAhEBAxEB/8QAHQABAAMAAwEBAQAAAAAAAAAAAAUGBwMECAIBCf/EAFgQAAEDBAADAggFDgwDBgcBAAEAAgMEBQYRBxIhEzEIFBUXIkFWlBZR0dLTIzI2QlRVYXF1gZGTlbMzNDVDc3SSobGytNQ3YnIJJERSU6MYJldkg6LB8P/EABoBAQEBAQEBAQAAAAAAAAAAAAABAgUDBAb/xAA1EQEAAQIBCQYEBgMBAAAAAAAAAQIRAxIUITFRUmGRoQQTQXGx0QUjM2IiMoGSweEVwvBC/9oADAMBAAIRAxEAPwD+qaIiAiIgIiICIiAvxzg0EkgAdST6lGX29G1RwxU8BrbjVO7OmpQ7lD3etz3aPJG0dXO0dDoA5xa10c3CKe5ObPkEpvlTsO7KYapYiPVHDvWt+t/M7/m7l7U0RbKrm0dVttSUmTWeJ5a+7ULHDva6pYCP718/Cqyffig96Z8q/I8SscTAxllt7Gjua2ljAH9y+vgtZfvRQe7M+Ra+Tx6Gh+fCqyffig96Z8qfCqyffig96Z8q/fgtZfvRQe7M+RPgtZfvRQe7M+RPk8ei6H58KrJ9+KD3pnyp8KrJ9+KD3pnyr9+C1l+9FB7sz5E+C1l+9FB7sz5E+Tx6Gh+fCqyffig96Z8q5afILXWSCOC5Uk8h6Bsc7XE/mBXH8FrL96KD3ZnyLiqcMx+siMdRYrbPGftJKONw/QQnyePQ0JlFVzi8+Ojt8dmkjjYNutE0pdTSj4mF2zE74uUhnxtPeJuz3aC9UDKqAPYCS18UreWSJ4OnMePU4Hof/wCrFVERGVTN4/7WlndREXkgiIgIiICIiAiIgIiICIiCsY9q7ZRf7nJp3isotlN/yMa1r5PxF0jiD8Yjb8WhZ1WMOHilxyahdsSRXJ042NczJY2PDh8Y5i9v42lS2QZHacTtU10vl0orNbIS0S1twqGQQs5iGt5nvIA2SANnqSAvox/z28LR6LOtIqPyC+0WL2G5Xm5S9hbrdTS1lTLyl3JFGwve7Q6nQBOgqf8A/ELwr/8AqXh/7epfpF8TcacAyWlqrXY8txTKbvVQSR0tkhvdK91c/kOodBzuju49DoE9F86KJnXhM19PwGyzOsewvIKGot9BFWUBv1FEyGeOUEsnHLP6UbQOZw2HgFvo9VeK3i9W23Erbd5uHmYz1lZK6HyPSUlNNVxco32knLOYmsOuhMmz0Gt9FiVs4H5xfeHXFbGKWwzYLjN6srKWyYxc7xHXspq7UhldC9jniGB31MBm+h2Q1o6KxcQbHnnEyDB6++cNquqstulqo7xhRvdJ/wB7e6KLxepc4SiKWJjxMOzc7fUO5TrSC61/hOYpRYVi+TR0N7rafIbsbHTUNNRbrIq0Cbmhlic4Frg+B7DrfpEfa7cIWHwgcjn452nETw+yCmtdZY/H5Y5Y6TxqnkNUyLtpCKot7FjSdhvM/bhoOVJwLglmVjsOC26oxentTbLxGq77LTUdbDJTwW+WGqcx0Z20lrHVDIuXlDttJ5eXRWkZzYcssPHmyZxYcbdlNtmsEthrKeCthppaRxqY5mTfVXND2aDgQ0l3d0KDZEVAf4QXC2N7mP4lYg17TotdfaUEH4v4RHeEHwtY4tdxKxBrgdEG/Uux/wC4gv6rEBFo4gTUzAGw3ejdWFo3/DQOjje74tuZLCP/AMf41YqWqhrqaGpppo6inmYJI5YnBzHtI2HNI6EEdQQq9VN8c4j2/lDtUFsnfKeXoDNLEI+v4oJen4F9GDrqidVp/rrZYWZERfOgiIgIiICIiAiIgIiICIiCBvluqKW5Q3y3Q9vVxR9jU0wOjVQbLg1pPTtGuJLd9PSe08vPzN71uulvyKjMtNIypi3yvjewtdG7v5XscA5jh62uAI9YUgoa7Yja7xVCrlhfT1wAHjlHM+nmIHcC9hBcB19F2x1PTqV7xVTVERieHj/3/fxfNIeTKM/+Eg/Vj5F9MoKaJ4cyniY4dzmsAIVe+A8o6Mye/Mb6h4zG7+90ZP8Aevz4ET+1N+/XxfRK93h7/SVtG1aUVW+BE/tTfv18X0SqnFi23XDOGeT3225TeTX263zVMHjE0Rj52sJHN9THTf4Qnd4e/wBJLRtaoiq3wIn9qb9+vi+iT4ET+1N+/XxfRJ3eHv8ASS0bVg8m0h/8LD+rHyJ5Nox/4WD9WPkVf+BE/tTfv18X0S/fgM54DZsjv0zPW3xsR7/OxrT/AHpkYe/0lLRtSV4yGlsnZU4HjFfMNU1vgIM03q6D1NHTbjprfWQvnHLNLbY6qqrHRy3SukE9U+LZYCGhrY2b68jWgAd2zt2gXFctlxu24+2TxGlEUkmu0me50ksnxc8jiXO/OSpNZqqpiMmjntPIREXigiIgIiICIiAiIgIiICIiAiIgIiICz3whC0cDs7LiQ0Wep2R/Rn8I/wAQtCWfeEHvzHZ1otB8j1P12tfwZ799P09EGgoiICIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgLPPCGAPAvPAXBg8jVPpOGwPqZ6laGs88IbXmLzzZIHkap2QN/zZ9XrQaGiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIoLIckfa54aGhpW190nY6VkL5OzjjY0gF8j9O5RsgAAEk70NNcWwxvmX7OqGya9W6mb6NfTR2euuMrRHnK2XZFSPLmYfcNj96m+jTy5mH3DY/epvo1vNa9sc4LLuvK3h3eEPW8GcQNilxB93tGUUE9Gy7srxGKefRDmOiMTt+i5rgSfS9Ia9HruXlzMPuGx+9TfRrO+PfC68eEDw3rcRvNNZqWOWSOenrYp5XSU0zD0e0Fmj0LmkfE4pmte2OcFlm8GvjfU+EHw1bl82NS4vBNVy09NTy1XjHbxsDfqodyM6F5e3Wj9ZvfXpqqy7CrZfsAxK0Y5Z7XY6e2WumZSwM8Zl3ytGtnUfUnqSfWSVNeXMw+4bH71N9Gma17Y5wWXdFSPLmYfcNj96m+jTy5mH3DY/epvo0zWvbHOCy7oqSL7mDTs2+ySAfa+OTN3+fsjr9BVhx6/x36lkd2T6Wqgf2NTSyHbopNA62OhBBBBHeCO47A868CvDjKnVwm5ZKoiL50EREBERAREQEREBERAREQUasO+JlxB7haKTX4NzVO/8ApZRNX/xOuX5Ho/31UpZdarVT5R6LIiIsIIuiL5bzezZxWwG6inFWaISDtRCXcgkLe8NLgQD3Eg/EV3idDaAiicVym15tj1FfLLUmstdawyQTmN8fO3ZG+V4Dh1B7wFLKAiIqCj8MP8A82ZWPVzUp/P2R+QKQUfhn2W5X+Ol/dFWfpYnl/tDUapXNERctkREQEREBERAREQEREBERBRqv/idcvyPR/vqpSyiav8A4nXL8j0f76qUsutVqp8o9FliXF2kuWQ8deGuOxZDeLLZq223iavgtNbJSmqEfivIC5hBaQXnThpwBcARzFZo6m4kcS8pz2lx+4VtO3GLibHbCcwnoDSiKCMsnmgFLL40XlxeXzPPMNt0NEn1BXYjablk9qyGppO0vFrhnp6Sp7R47KObk7UcoPKebs2dSCRrprZVXy7gHgedZBLe7zYG1FzmjbFUTQ1U9OKpjfrWzNje1swA6ASB3Tp3LwmmZRltjwmpuHhU0dVkNyuDMghwi3V1Yy2XWoipX1TKp7JGtY1zQ6Aubvsy3lJcSW7cdyPCDFKzPrnxFuN7yzJ5m02VXe2UVJTXmop4aWn+s5WtY4bI5yWk75C1pZykbOqZVwoxbNL9ar3drY6W7WwclLWQVU1PI1nMHcjjE9vOzmaDyO23fqUrjeI2nEW3JtppPFBcq6a5VQ7R7+0qJTuR/pE62R3DQHqASKdI8g4bmeecQMe4R4nS3GurXV2Lz3qsqJMjmtdVcJmVAiDTWNilkdyNJcWN5S7mBLtN0b7PNn3Byy4tlmbX2R9ps99morjCy5yVkYtFUGxwy1MhjjEssE/L9UMe+QnZ6lavXcAMBuOJ2LG5rAPJdi35M7Ornjno99/Z1DXiUb9fp9em96CmoOGOMU+BzYW20ROxmaGSCSgke94eyQlz9ucS4kuc4l297O97UimR5nrMh4g3ocP7Qysr46niBPdMjmpZb7LbJIoGiN1LQw1DYpXQhkL2vcxjWkua7qNu3vPBCwZpjdkutHmNW2qHjxfbGvub7jPBTFjPqclQ6GJ0hEgkILm75XAEnW1Yc44aYzxHtNNbchtUdfS0srZ6Yte+GSnkA0HRSRlr4zrptpHTou1h2FWbALI202KjNFQtkdLyOmfM5z3HbnOe9znOJPeSSVqKbSJxR+GfZblf46X90VIKPwz7Lcr/AB0v7or0n6WJ5f7Q1GqVzREXLZEREBERAREQEREBERAREQUar/4nXL8j0f76qUsuLJbJXNusd5tcbaqoEHi09G+Tk7WMEuaWOPQOaXO7+hDj1Ggop11v7XEDDrm4A94qqPR/95daLYlNMxMaojTMRq82rXTSKE8rX/2NufvVH9Onla/+xtz96o/p1e7+6P3U+5ZNooTytf8A2NufvVH9Oo3JM3uGJWC4Xq64pdKa20ED6iomE9K/kjaNuPK2Yk9PUASnd/dH7qfcstqKE8rX/wBjbn71R/Tp5Wv/ALG3P3qj+nTu/uj91PuWTaKE8rX/ANjbn71R/Tp5Wv8A7G3P3qj+nTu/uj91PuWTaj8M+y3K/wAdL+6K6oumQOOhh1xaT65KukDR+PUxP6AVYMVsU9qZWVdaYzca+QSzNhcXRxAMDWxtJ0SAB36GyXHQ3oYxJijDqiZjTo0TE+MT4eRqhOoiLlsiIiAiIgIiICIiAiIgIiICIiAiIgKgcfxzcEs4Gt7tFR01vfoH1aO/0H8Sv6z7whG8/A7OmkF27PUjTRsn6me4dNoNBREQEREBERAREQEREBERAREQEREBERAREQEREBERAWeeEMQOBeeFx00Wap2db/mz6j3rQ1n3hBBx4H50GF4f5HqdGMbdvsz3fhQaCiIgIiICIiAiIgIiICIiAiIgIiICKKvWVWbHDGLrdqK2uk2WNqqhsZf8egT1/MovzqYd7UWn3yP5V7U4OLXF6aZmPKVtK0oqt51MO9qLT75H8qedTDvai0++R/KtZtjbk8pXJnYtKKredTDvai0++R/KnnUw72otPvkfypm2NuTykyZ2LSiq3nUw72otPvkfyp51MO9qLT75H8qZtjbk8pMmdi0rIPCbz3FrBwoy+zXfI7RbbrWWac09vrK6KKedrmuaCyNz2ucCQQNd5BG1dvOph3tRaffI/lXlH/tCcKxnjNwypL3j12ttxyuwS80NPTVDHzVVO8gSRtAO3EHleB+B3xpm2NuTykyZ2PYGM5hYc1oJK7Hb3br9RRymF9TbKuOpjbIAHFhcwkB2nNOu/Th8al1538FG1YZwH4JWPG5cmtAusoNfcyKyPrVSAc4+u+1Aaz8PJta/51MO9qLT75H8qZtjbk8pMmdi0oqt51MO9qLT75H8qedTDvai0++R/KmbY25PKTJnYtKKredTDvai0++R/KnnUw72otPvkfypm2NuTykyZ2LSiq3nUw72otPvkfyp51MO9qLT75H8qZtjbk8pMmdi0oqt51MO9qLT75H8qnLVerffaY1FtrqevgDiwyU0rZGhw72kg9CPiWKsHEoi9VMxHklph3URF5IIiICIiDPcPcK+inusoD62sqJjLMeruVsr2sYD6mtaAAB07zrZKn1XsB+xeD+mqP371YV2MbRiVRxWdYiIvJBERAREQEREBERAREQEREBERAUNUltszPHamAdlNcKiSgqCwfw0Yp5pmh3xlrovRJ2RzOA0HO3MqEvX2UYV+VpP9BVr0o05UcJ9JWF+REXIQREQEREGd4D9i8H9NUfv3qwqvYD9i8H9NUfv3qwrs431avOVnXLCK/wlLoLjSVNvwsT4lVZNHi8V9qLoI3OnNR2D5OwEbiI+cPa13NskDYaDsWHFOMGRZrl2T2y14Wx1rx67T2qquc92DDK5kLZGmKPsjzOJc0FrnNDeZp5ndQ3znJeaPE+NldTtbbcriZmDqymxGgulfFPTVD5+TxplC+m7MuYHOlc4ymMuDntLdjXoqzcIbrT4nxZs09ziopcwulwq6KsonOc+ljqKaOFjnbDdPa5hOgSO7Tvi+SJmUReAeEpFkmdVeK5Ba7ZYq+KgmuDZLdf4LoyNkLmiWOfswDDI0PDtEEEB2ndFUMg41Zbm9ZwsulDjdbjOHXrK6QUl08qhs9wpXRTlrZqZrQWxyAB4Bc4aaNgdFIYvwAyoZBiMt6pcStljtFkrcfqKCwGYPkhniY0zNc6MAuLomegR6Ic487j0X5Z+DHE2K38N8culfi9Xj2E3ekqoK+F9Qytq6WnikijDoywsbIGPG9OIJG9j1vxDhuvhs49brlWVDKe0z41R1zqGWqOR0rLk7ll7J80dvPpujDtkbcHOaOYN0Rv0ksO4f8K874YVLcdtD8VuODsuclVBVXFk/lGnppZjLJByNbyPcC94bIXjWxtp1pXGo454vTTyQvgyUvjcWO5MUurm7B10IpiCPwg6Womf/Qz/AIy8a8gnx/ibbMKxqouNNjlsqYLlkTLo2jNFVGmMmqdvKXSPia5j3aczR0ASV3rPxoyCSktOPYvisubXq349QXK8VFTdG0jYjNFuNge5jzJM/ke7R0Na27qom+cKM7qaDiJS4TWWCXFOIMM1cWX9tVTVlBUVFK2GQhgjJLXBrTyvDXMOwQdaMnScJ8+wG7uu2E1eOz1d0sdvtl0gvT52xxVFJG6OOohdGwl45XkFjg3fK08w7lnTcdul8JB2ZfB6lwDGJcout1tIvctPWVraCKgpy8xATSFr/qhka9gY1p+scdgDatlh4mVtw4iUmIXKweSq+THm3yc+ONm7B5n7Ewei3Ttd/OHde7XrWcYx4PuVcHanHrhgVztF0rYLIyy3amyDtYIqstmknFRG+Jr3Mf2k0vokEFrgNgjaslywTiBDnVkze3y41VX82J1lu1JUyVEFKNzCZssDg17jyu5hyO1sH64K3nxEbbvCQumQRYdDZcMZXXTJZbzFDBLdhDFAbfUiEukkMRPK8bd6LSWnTdO2XDr2TwmbxW0VBdbngTrVYnX4Y1X1fleOaWlrO38X22MRgSQiXTS/ma7qSGEDZ/OF/AXJsNreGlTdbhaqqTGzkDq99I6QCZ1fUiWIxNcz1DfMHEaPQF3euWXgRf38Oa6wCstvjk+bnJWv7WTsxTeUxV8hPJvtOzGta1zdObXVT8Qisx8M2x4xe7/FT0lprbTYaqSkrpJsjpaa4SPjOpvFqJ/pyhp2BtzC8tIaD0JnMg8Iq7UdwznyHhQvtpxCGCsra911bTmamkpGVXNDGY3F0ga53oEtBDR6W3coWDhZnfD3Ib5SY1JitxxS7XiW7h96ZOK2hM7w+eJjWNLZW83MWEuaRzdd6UpWcIrvPVcapGVFC1ma0scFuBe/6iW28Ux7b0PRHON+jzej+Hor+IQzuKWZXXwg7HarDb6O4YhX4xHdQyouHi7uzkqIg6p0IHEva13KIuYBwJPM0qxcL+Mlz4n5FeIqXF46THrdW1VvdcX3WN1U2aGQsIlpeQOi5iCW+kTrRIGwoaLhRmWL5Jg+QY9PY6qttmMx41dKW5yzRxOY10T+1gexhJcHRuGnNAII6gr9tPCfLarjpQ5vdY8ZtFPQtrIZJ7B24q7vDIOWCOra5ob9TGnb5n+k3pyjomkbWoS9fZRhX5Wk/wBBVqbUJevsowr8rSf6CrX0YeufKr0lYX5ERchBERAREQZ3gP2Lwf01R+/erCoDEQ23Uk9omcGV1HUTc8LjpxY6V7mSAHva5pBBHTvG9g6n12MbTiVTtlZ1iIi8kEREBERAREQEREBERAREQEREBQl6+yjCvytJ/oKtTahZyy7Znj9NTuE0ttqJK6p5DsQsNPNC3m+IudL0B0TyuI2GnXpRoyp4T6SsL6iIuQgiIgIiIIy84xZ8jEYutqormI98njlOyXl3365gdKJ81mGeyVk/Z8XzVaUXtTjYtEWpqmI81vMKt5rMM9krJ+z4vmp5rMM9krJ+z4vmq0otZxjb885XKnaq3mswz2Ssn7Pi+anmswz2Ssn7Pi+arSiZxjb885Mqdqn13DrA7XRVFZWY1j9JR08bppqieigZHExo25znFugAASSegAUbZuE+K19ZJdKzD7JTuPPDSxQxNkjdASC2R7OUM7R2t9xLR0Durt2KoM9+vop2PqqS32yYeNRy0beyuDnRbaxr37JYzna8uYBt7Wt5/QlYbAmcY2/POTKnaq3mswz2Ssn7Pi+anmswz2Ssn7Pi+arSiZxjb885MqdqreazDPZKyfs+L5qeazDPZKyfs+L5qtKJnGNvzzkyp2s2r+GuGYvcn1s2L4/FZax5fWVVa1gFLNyxxxcjXtLWxv5dFrS0B5B5XGR7hYPNZhnslZP2fF81WaWJk8T45GNkjeC1zHDYcD3ghQuPzVVBVT2etmrLhNC01EdwmpBHHJE+R3LHzs9EvYAAejSRynR2SmcY2/POTKna6fmswz2Ssn7Pi+anmswz2Ssn7Pi+arSiZxjb885MqdqreazDPZKyfs+L5qeazDPZKyfs+L5qtKJnGNvzzkyp2qt5rMM9krJ+z4vmqetloobLTeL2+ip6Gn5i/sqaJsbeY950ABv8K7aLFWLiVxaqqZ/VJmZ1iIi8kEREBERARR18qZKWjD4nljucDY/EVA+WKz/13foCC3oqh5YrP/Xd+gJ5YrP/AF3foCC3qIvlznhmpaCgZBUV9U8F0T6psLoqcOAlnA0XO5A4AADq5zAS0EuEM69VbGlzqhwAGydBdeKokZXS1+oxWyxthfUiJokdG0uLGF2tkAveQD0Bc74ygtlntNNYbVSW6ja9tLTRtij7WV0ryAO9z3Eue495c4kkkkkkkruKi2zMGXuCSe3XWCvhjlfA+SllZI1sjHFr2Et3pzXAgjvBBBU5Yq+oqqxzJZS9oYTo/HsIJ5ERAREQFEZJajcKSKohifNcKB5q6Njal1OHyhpAY94B9BwJa7bXDR3rYGpdEHUtVeLnboKns+xdI304TIx5ieOjoy5hLSWuBadEjYPVdtVqkEeOZXLSDyTQW27fVqaGMmOqnrfTdUEt+teDGGO9HTttkJB3sWVAREQEREBERAREQEREEVkf8nt/pB/gV5m4p5plGL8VaBtwyGpw/ApIKdtPc4LVFV0s1W6UiSGrlcC6AEcgY4crduO3bGl6ZyP+T2/0g/wKxbiFwVt/Eu4Ofdr/AJAy0zRxRVVipa1rKGqbG/nAewsLhs95Y5pIAB7kGRXfijxRzLIMzqMNo70Kaw3SotFBR0Vvts1FUy0+g7xmSeoZMOd+/wCDDeVpaRzFffGbi7mVjdeLhjl2ujLjj1phuF1x+ks1LU0NDIYu1dHV1Uj2vPM31QkuA9LR2FqNy4DWuoyi5Xu2ZDkeNG6zNqLlQ2WvEFNWSgAdo5pYXNcQ0Bxjcwu112vjL/B8sWZXfIKue73230mQxMiu9st1YIaauLY+za945C8HkDWnlc0ODQHA9dhXaDIswz3jRdbPQZPLj+N0dktd18XpqKnlmMkzpuZgkkY7TXBmnbBPot5S3rvm4T3XN+LNNT5wcuFnsNRcqhlNjlPbYZGOpYah8OpZXDtO1f2ZJLXAN2PROtK8Ybwxt2HX6rvFPW3Csrqm20VplfWvjIdHSh4Y/TWN9N3aOLj3HpoBQ9s4E2ywZHLcrPkWSWe3y1/lKWw0VeG2985fzvPIWF7WudsuY14adnpo6QYjiN5y/h/gt1zGgyRhsdNnNXSz466gjLJ4ZrsYJCZjuQSAy8zS0hoDQC09SfZ+Nfx9/wDRn/ELJZeBthmwOvxN1XcRbq27m9SSiSPthMawVnKDya5O0aBrRPL03vqtaxr+Pv8A6M/4hBZUREBERAREQQWZwyeQ31kE9PSVNve2tZUVNN4w2NrDuTTR6W3RdozbfSHOdb7jK2+4U12oKauo5mVFJUxNmhmjO2yMcAWuB+IggrsKuYNcBVWyro33Vt3q7ZWzUVRM2k8V5HB3MyPk7vRjfGOZvR31w1vQCxoiICIiAiIgIiICIiDjmgjqGcsjA9u96IXD5MpPueP+yu0iDq+TKT7nj/sp5MpPueP+yu0iCsYTbYH2APlpbgx7qqqdy3kN8YANRIQOnTk19YPUzkU75MpPueP+yofAYPFsYij7O6RaqKk8l5cHVPWokOyR9qd7Z/yFisSDq+TKT7nj/srkho4Kd3NFE1jta20LmRAREQEREBERAVdtNx1m2QWyW8PrJmQUldHbnU3IKOGQSRjlk1qQPfTyu13tPf0LVYlXZrh4vxCpKF91lHjdrmmitXi22O7KWIPm7XXQjt428h7+bY7igsSIiAiIgIiICLjqKiOkp5Z5XcsUTS9zviAGyVQ4J79k1NDcRfamxwVDBLDR0MEDixhG287po3ku136DQO7rrZ98LBnEiZvERx/q6xDQEVB8jXz21vPu9D/tk8jXz21vPu9D/tl75r98dfZbcV+RUHyNfPbW8+70P+2TyNfPbW8+70P+2TNfvjr7FuK/LN/CGy3McC4S3zJMHo7dcb3ao/G3Ulzhkljlgb1lADJGEODduB39qRo7C7Xka+e2t593of8AbL5ksV6mjcx+Z3h7HAtc11NQEEHvBHiyZr98dfYtxYD4AnHniNxxor9LkVBaYMWtheyCsp46jxmarllMpZzySvBYxrnDQGwDGN9Ovr5Y7w+4P0/CvHG2HFcgutntTZZJxTxQ0b9yPdtzi51OSST8Z6AADoAFZPI189tbz7vQ/wC2TNfvjr7FuK/IqD5Gvntrefd6H/bJ5Gvntrefd6H/AGyZr98dfYtxX5FQfI189tbz7vQ/7ZPI189tbz7vQ/7ZM1++OvsW4r8iobLXfYiXNzG6Su9TZ6ajLO/1hsDT/eFYcUvk17oJxVxMir6Od1LUiLfZl4DXBzN9eVzXNdo71vWzrZ8sTAminKiYmOF/5iEsm0RF8yCrt3uHima47Tuu0lK2qhq2C2im52VbgI3B5k+0MYDtD7btD8SsSrmQ3HxLJ8VgN4fQCrqZ4hQim7QVxFPI/kL/AOb5Qwv36+XXrQWNERAREQEREEXlX2MXj+pzf5Cq9jP2OWr+qRf5ArDlX2MXj+pzf5Cq9jP2OWr+qRf5Aujg/Rnz/hrwSSIi0yIirmV53QYfdsYt1ZDUyz5BcDbaV0DWlrJBDJLuTbgQ3licNgE7I6esQWNERUEREBERARVy4Z3QW3PbNiMsNS65XWiqa6CVjWmFrIHRNeHHm2HEzN1oEdDsjpuxqAujw+/lDMfyw3/RUq7y6PD7+UMx/LDf9FSrU/Sr8o9YWPFcURFy0FXMluXiOQ4lAbybb45XywCjFL2ouBFJPJ2Rfr6ly8hl5vX2XL9srGq7ktxNFfsSgF4dbRWXGSE0jaXtRcNUdQ/sS/X1LXJ2vP032PJ9ugsSIiAiIgIiIIvKvsYvH9Tm/wAhVexn7HLV/VIv8gVhyr7GLx/U5v8AIVXsZ+xy1f1SL/IF0cH6M+f8NeDs3O2015ttXb6yJs9HVwvgmid3PY5pa5p/GCQvFVgynM8So6O7zNrH2bgo59kutO1vW7QvldE97fj7KjbSTDeurivby6ctmt89PXwSUNNJBX83jkboWltTtgYe0GtP2xrWne+gA7gkxdl4wueK3YN4VY7ktdZ7ZR5RTXLIbmMip5ZqGru88jJuxmbHPDt0ccjmsDnFvoH0SQCJO9cPrTY7Rw0tF/yezZPilbnczo20L3w0FHH4jUMdSMc+eQ8glY70S/veW610XrXIMYs+W211uvlpobzb3ODnUlwpmTxEjuJY8EbH4l0p+HuLVNlprPNjVnltNLzGCgfQRGCLbS13JGW8rdhzgdDucR61jIGTeDrT2+y8Q+KVgxSp7fArdUUBt0UM5npqWrkhe6rhheSdNB7FxYDprnkaHcrh4Q+KszPhbX2l9+ose7aopnCe5SmOkqHNmYRTTEOaeSUjkIad+l033GbuHD4U9lorVid1mwGipnOcIbBQ0bY3A+rklge1o319EA/GurQcM552VVNlWTVud2moi7N1qv1vt5p+bmBD+WKmYSRojRJHU9N6I1bRYeV7xkFHk1lwHE7fQWXB8UGR3W0XqmrJZa2yS3CGJj4Yw+OWEvhkL3ua0ua3naAWnl0rNcsYo8E4eVNgmuGM5pacnyeltlDbaaSels1mqOyc+QSf94lfyHsw4wh4Be8AABy9PHBMaONfB049ajj+uXyT4lF4pre9dly8nf17u9cY4e4sMadjoxqzjH3HbrT4hF4oeu+sXLy94B7vUs5A8Yx29o4f5nhbbtRVVqt/ESxU1ObBLJHT0rZpKUyNp+aWR8QDy/oHnleHa13DVs4wfC7bxnw/Br5RUVm4cy2etuFJanP8Xoq+69tEHCUbAke2IucGu31JPet3h4d4pTM5IsZs0TN055WUEQG4CXQHo3+bJJZ/5SSRpd3I8VsmYW/xC/WegvdDzB/i1xpWVEXMO48rwRv8KuSPPOQcOcDynjFwux6jpKS64dDYb2YaSnq3S0zy2opQ5hIeedrXl3oEkAtHT0Rq+eC+59NgN5tAlllorJkt3tNCJpHSOjpoayRsUfM4kkNbpo2e4AepaPb8QsNploJKGyW6jkoIX09G+npI2GmieQXsjIHoNcWtJA0Dyjfcu3bLNQWWOeO30NNQRzzyVMraaFsYkme7mkkcGgbc5xJLj1JOyrFNpuO4ujw+/lDMfyw3/RUq7y6PD7+UMx/LDf8ARUq9J+lX5R6wseK4oiLloKuZPcRRX/EYDeXWw1lykhFIKbtRcdUdS/sS/X1Ll5O15vX2PL9srGq7k1zNFkGJU4u7rd45cJYjSNpe1FwApKh/Yl/80ByCXn9fZBv26CxIiICIiAiIgjMoaX41dmgbJpJgAP8AoKruMkHG7SQQQaSLqDv7QK6OaHtLXAFpGiD61S34derX9Qsl1oo7c3pFTXCkfK+Ef+VsjZW7aPUCNgesr7sCunImiqbabtRqskkUZ5AzD76WP3Cb6ZPIGYffSx+4TfTL3+Xvx19i3FJoozyBmH30sfuE30yeQMw++lj9wm+mT5e/HX2LcUmijPIGYffSx+4TfTJ5AzD76WP3Cb6ZPl78dfYtxSaKmYJW5dm+NRXdtVZaMSVFTB2LqKZxHZTyQ732o7+z3+dWDyBmH30sfuE30yfL346+xbik0UZ5AzD76WP3Cb6ZPIGYffSx+4TfTJ8vfjr7FuKTRRnkDMPvpY/cJvpk8gZh99LH7hN9Mny9+OvsW4pNdLh80iuy93TTruCCD/8AZ0w/xBXGzHstedSXezRtPe5lulJH4gZ9f/719ysdiskFgt4pYXSSkudJLPMQZJpHHbnuIAGyfUAABoAAAAeeJXRTRNMVXmdl/M1JFERc5kVdyO5eJ5HilML15NNXWzMNF4r2vlENpZndlz6+pcvKJeb19ly/bKxKu365CnyvGKPy0aB9TJUO8nim7Tx9rYTtpfr6nyEtfv16160FiREQEREBERAREQEREBERAREQZ7wGeHcOI2gcpiut2hI6d7LjUtPcPjBWhLPuDT3U9Dllre8vkt2T3Nrg4klonnNY0df+Sqbr8BC0FAREQEREBERAREQFW7ldAzP7Da23c08k1BW1brWKXn8aZG+nYZO1/m+zdM0cv2/af8hVkVfgr31WeVdHFd+aGit0Uk9qFMPRfLJJ2cxm796hkbyDu7z3tQWBERAREQEREBERAREQEREBERBntK84lxlraeT0bdltIyqgeSdCvpmCOVnxbfT9i5oHXVNKTvprQlA5ricWY2TxQzuoqyCaOsoa5jeZ9LUxuDo5ANjY2NObsB7HPYfRcVwYVlkmQ01TR3GBlvyO2ubFcre1xIjcd8ssZIBfDIAXMf6xtruV7HsaFlREQEREBERAREQFXsNrpbzT3G6+P1FXQ1tY91FDUUog8XhY1sXK0fXPa58b5Q93UiXppoaFyZI6a5s8iUprIXVsb2T19BPHHJQxlp1IC7Z5iQQ3TT12emtqbjYI42sBJDQAC5xJ/OT1KD6REQEREBERAREQEREBERAREQFWcuxWe6y012tE7KHI6AO8VneSIpmHq6nnABLonaHqJaQHN6jquGeU1LVz09Hb7heHQOMcr6GNhYx472cz3NBI9YBOiCD1BC6vnEl9lb9/Yp/pl9MdmxZi9vRbSr8/hI4JZ77Yscv94ZYctu9T4jHYaiN76iOo21vK/kaQ1ji4ckjiGPBDmkhagv5p8afAy4g5LxNnzrGr1eb3dpqwVpkyVsMM8bmuBY1skL3NIaAAAGMaAAA0ABe9LfxGrn0FM6txK8xVhiaZmQincxr9DmDSZgSN70Vc1xdnWPdbSvCKn+cSX2Vv39in+mTziS+yt+/sU/0yZri7Ose5aVwRU/ziS+yt+/sU/wBMnnEl9lb9/Yp/pkzXF2dY9y0rXVVUVFTTVE8gighYZJJHdzWgbJP5ln+D8a7Bxfw+hu+DV/lB1ybLFHI+ldKy3zsaOZtW1rm8haXN9DnaXggsJaQ9VzjdkGTZhwlyqw4rjVzgv1zoX0VPLXGGOFgk9B5c5sjiNML9aB66XnfwOvBtzHwcsmnv91uF7kFXEYKyw2mCB9HVN0ezdI+SUHmY4lzS1oI24c3K5wczXF2dY9y0vcdstcNsZKWMjNTUPEtVUNjDHVEvI1naP13nlY1v4A1o7gF3VT/OJL7K37+xT/TKRsuY0t4rBRyUtXbK1zS9lPXRhhkA1ssLSWu1sbAOws1dnxaYvMeiWlPoiL50EREBERAREQEREBERAXHUPMdPK9vRzWkj9C5Fw1n8Un/6Hf4KxrFDwAD4C484DRfb4Hu6724xtJJJ7ySSVPqA4f8A2B43+Tab901T66+N9SrzlZ1yIiLyQREQEREBERAUFljzELLK3pIy70Ya74uaVrHfpa9w/Op1QGYfxe0flig/1DF64X54ap1tCREXHZEREBERAREQEREBERAXDWfxSf8A6Hf4LmXDWfxSf/od/grGuBQ+H/2B43+Tab901TxPKCfi+JQPD/7A8b/JtN+6ap9dfG+pV5ys65eb8P4951k9y4Z3eamx6mxLOLhUQU9JBFNLcKWJkU0jA93aBjnHsvSIaAw9NOUTavCwyXIZKa/WixNuWNVNcIYbTT2C6vrpKXtuzM4rBF4tza3Jyd2hy8/Mq5wlxnIsO4uW+a0YxW1z6m5ztuM96w6S2mippXOdNNFV+Mvg5ieX0YWAP7tBbZg3BTIeG9bBbrBnj6bBYK19XDYJrVFLNEx8hkfTtqS7Yi5nO16BcAdBy+SMqUU+t48Z5QWXJ8sfRY6cXxzKZ7FU0bY5/HamnZWin7Zj+fkY9rXtJaWuDi1x23YaODNPCYyNmV5VRYrbqaejxyqdQOgqLFda6W5VDGNfIxk1LG6KAbdyDn5zscxDWkbuty8H7yhw1zPEvL3Z/CO/z3zxzxPfi/aVbKnsuTtPT1ycvNsb3vQ7l91nBXIbRluQXXCs7fitDkNQK25W6W1R1oFTyNY+aBz3Ds3Paxu+Zrxsb16lbVCMs3FTO864lzWGx2+0WO1w2O13qd97pZ31cBqe0L6d0bZGenpmtnXIWHYdscsFcfCNvdi4tUFkmq8cvlirMgbYXxWekrTUUTpHFsZlqyDTmQHl54hpw2db0Vrlm4feSeKGR5gbh2xvFvoqHxPseXsvF3THn5+b0ubtu7lGuXvO+maN8GW7U1HbbTSZ2YMds9/bkNrt5tEbnsmFUagsnl7TczNvkA0GH0gSXcujZiodHBsvy3HuIXG69ZBe6GrxLHq11RNSNpZzPHCy3xTMbA505YwBpHM3kPM/ncOXm0Otw98I7MMmyLFzWWKOqs9/mZG+loLBdoZrUyRhdHJJVTxCCZoPK1xbyD0tt5gFoE3BWpOd5Xc4sgb8FssYBe8dqKASGd3i3ixMc4eDGHMDCRyu6t6EbX3wz4W5bw+fbLdUcQZL3itrhNNR22a0xR1BiDeWJs1SHEv5BrRaxhOhvaWkaeoDMP4vaPyxQf6hin1AZh/F7R+WKD/UMX04X1Iap1w0JERcdkREQEREBERAREQEREBcNZ/FJ/8Aod/guZfL2CRjmO6tcNFWNEjP+H/2B43+Tab901T6q1quMeE2eis13ZUQTUELKZs7KaSSKdjGhrZGuY0jqNbb0IOxrQBPP8P7H90z+5zfMXbxMOuuuqqimZiZamJmViRV34f2P7pn9zm+Ynw/sf3TP7nN8xY7jF3Z5Slp2LEirvw/sf3TP7nN8xcVVxKx2hppaipr309PE0vkllpZmsY0d5JLNAJ3GLuzyktOxZ0Vd+H9j+6Z/c5vmJ8P7H90z+5zfMTuMXdnlJadixIq78P7H90z+5zfMT4f2P7pn9zm+YncYu7PKS07FiUBmH8XtH5YoP8AUMXx8P7H90z+5zfMX0yT4a1ttioIqjxGmq4quoq54HxMAjPOxjOdo53F4b3dAA7ZB0Haporw5iuuJiI2rETE3loaIi4jIiIgIiICIiAiIgIiICIiAiIgIiICzzwhgHcC88Djpps1Tsj+jK0NZ94QevMdnWwCPI9T0JA39TPrPT9KDQUREBERAREQEREBERAREQEREBERAREQEREBERAREQFnvhCEN4G52XEhos9Tsg6I+pn16Ov0LQlnnhD68xeeb1ryNU75t6/gz8XVBoaIiAiIgIiICIiAiIgIiICIiAiIgIiICIiAiIgIiICz3whQDwMzsHqDZqn7YN/mz6z3LQl41/7SjGMwh4bUmY4rkl9ttFQHxK82233GaGnnp5Dpsj4muDTyvPKSQSQ9vqag9lIsS8DrFssxrgTZZs2v12v2Q3cm6zPvFZJUy0zJGt7OEOkcSAGNa4t9Tnv6LbUBERAREQEREBERAREQEREBERAREQEREBZNnvFmpFVNbMcljjMTjHPciwP5Xg6LImkcpIPe47APTR66tHFjIZsdwuqfSymGtq3so4JGnTmOedOcD8bWc7h+FoWERRNgiZGwcrGANaPiA7l+m+E9goxonHxYvETaI911Rd+1jqi5PL664V9dISSXVFXI79A5tAfgAAXV8lU/xSfrn/Ku2i/YxEUxaGcqdrqeSqf4pP1z/lXHUWGhrIHwzwmeGQcr45JHOa4fEQT1UblWf2HCn00d3rjBNU8xhghgknleG65nCONrnco2Nu1obHVR9VxfxCkp7ZMby2dlzhknohSwSzuqGsc1rwxrGklzS4bbrmGndPROsTi0UzMTVGjiZU7Vm8lU3xSfrn/Knkqn+KT9c/5VA1HFDFqXE6fJZLxCLNUPEcNQGPc6STmLezbGBzl+wRyBvN0PToVG8NOJI4h3TKxTmJ9stldHTUkrYZIpHtMDHu7Rr+ocHucNabrXcp31GVFMTpkyp2rh5Kp/ik/XP+VSFur7jZJGyWy7V9C9vc1tQ58Z/HG8lp/OFxIt1UxXFqovBlTtbJw74ofCOdtpu7Yqa7lpMMkWxFVgDbuUEnleBslhJ2PSBIDg3Ql5Wm7ZrWyU0hhqonCWCVp0WSNO2n9IC9K4ve25JjlsurWhgrKaOcsB3yFzQS38x2PzL8R8W7DT2aqMTC0U1eGyf7a1xdKIiLgIIiICIiAiIgIiICIiAiIgzXj1SvlxS21I/g6S5wySH1AOa+If/tK1ZGvS99stNkVmrLZWNLqaqidE/lOnDY7wfUQeoPqIC853my1uMXR9suTQ2paC6OUdG1EYOu0b/dsd7SdfET+0+C9opqwpwJ/NE3/QnTDqIqtcuFWGXmvnrq/FbRWVk7ueWeeije97vjJI2Suu7gzgbzt2HWNx0Bs0EXcOg+1XfmcTwiOf9MKLxJx6ei4sx5DXUOS19iqrQyhbLjFRUtnppmSvfqRkDmucxwf39QC3rrvXPi2IRWfiDhdVabLdqC1Otlznn8pdpLLDNNLA7Uz3Odp7iHHRds9fwrVrLY7djlujoLVQ09uoYySynpYxHG0k7Omjp1JJXeXzx2aMrKnXe/WJ17NCvOtFYrzjdws+QzY/cq+3WnKr5LNQ01K584iqHvENRHERt7R8bd9H7G1f+Ehqq3JuIF1ntdwtdNcLnBLTNuNK6B8jBSxMLg13q20/i7jo7C0tRORYjY8uihivdoortHC4ujZWwNlDCehIDgdJT2fu5iaZ1af1tb0EsipvmYwLWvgbY9fk+L5qkrBw8xfFa11ZZsetlqq3MMZno6VkTy0kEt2BvWwOn4F9ETiX0xHP+kT73tjY5ziGtaNkn1Bb5wpo5KHhxj0UoIeaRkmj0ID/AEgPwdHLHcOw+bO7t4q1rhaYHjx+pBIGuh7Fp9b3Dv19a07JBLA70WxjY2Na1oa1o0GgaAC/MfHO0UzFOBE6Y0zw2Nxoh9IiL8mCIiAiIgIiICIiAiIgIiICjL/jdsyigNHdKNlXBvmaHba5jv8AzMcNOa78LSCpNFqmqqiYqpm0wMrrOAtMXk0N/r6eMkkR1Eccwb+AHTTr8ZJ/Cur5hKj2ok9xZ85a8i6cfFO2RFsvpHst2Q+YSo9qJPcWfOTzCVHtRJ7iz5y15Ff8r2zf6R7F2Q+YSo9qJPcWfOTzCVHtRJ7iz5y15E/yvbN/pHsXZD5hKj2ok9xZ85SFt4EW2KRrrndq+5MHfA0tgjd+PkHN+hwWnIs1fFO2VRacTpEekF3Wt1upbRRQ0dFTxUlLC3ljhhYGtaPwALsoi5kzMzeUERFAREQEREH/2Q==", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from typing import List\n", "from typing_extensions import TypedDict\n", "from IPython.display import Image, display\n", "from langchain.schema import Document\n", "from langgraph.graph import START, END, StateGraph\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of our graph.\n", "\n", "    Attributes:\n", "        question: question\n", "        generation: LLM generation\n", "        search: whether to add search\n", "        documents: list of documents\n", "    \"\"\"\n", "\n", "    question: str\n", "    generation: str\n", "    search: str\n", "    documents: List[str]\n", "    steps: List[str]\n", "\n", "\n", "def retrieve(state):\n", "    \"\"\"\n", "    Retrieve documents\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, documents, that contains retrieved documents\n", "    \"\"\"\n", "    question = state[\"question\"]\n", "    documents = retriever.invoke(question)\n", "    steps = state[\"steps\"]\n", "    steps.append(\"retrieve_documents\")\n", "    return {\"documents\": documents, \"question\": question, \"steps\": steps}\n", "\n", "\n", "def generate(state):\n", "    \"\"\"\n", "    Generate answer\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation, that contains LLM generation\n", "    \"\"\"\n", "\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    generation = rag_chain.invoke({\"documents\": documents, \"question\": question})\n", "    steps = state[\"steps\"]\n", "    steps.append(\"generate_answer\")\n", "    return {\n", "        \"documents\": documents,\n", "        \"question\": question,\n", "        \"generation\": generation,\n", "        \"steps\": steps,\n", "    }\n", "\n", "\n", "def grade_documents(state):\n", "    \"\"\"\n", "    Determines whether the retrieved documents are relevant to the question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with only filtered relevant documents\n", "    \"\"\"\n", "\n", "    question = state[\"question\"]\n", "    documents = state[\"documents\"]\n", "    steps = state[\"steps\"]\n", "    steps.append(\"grade_document_retrieval\")\n", "    filtered_docs = []\n", "    search = \"No\"\n", "    for d in documents:\n", "        score = retrieval_grader.invoke(\n", "            {\"question\": question, \"documents\": d.page_content}\n", "        )\n", "        grade = score[\"score\"]\n", "        if grade == \"yes\":\n", "            filtered_docs.append(d)\n", "        else:\n", "            search = \"Yes\"\n", "            continue\n", "    return {\n", "        \"documents\": filtered_docs,\n", "        \"question\": question,\n", "        \"search\": search,\n", "        \"steps\": steps,\n", "    }\n", "\n", "\n", "def web_search(state):\n", "    \"\"\"\n", "    Web search based on the re-phrased question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): Updates documents key with appended web results\n", "    \"\"\"\n", "\n", "    question = state[\"question\"]\n", "    documents = state.get(\"documents\", [])\n", "    steps = state[\"steps\"]\n", "    steps.append(\"web_search\")\n", "    web_results = web_search_tool.invoke({\"query\": question})\n", "    documents.extend(\n", "        [\n", "            Document(page_content=d[\"content\"], metadata={\"url\": d[\"url\"]})\n", "            for d in web_results\n", "        ]\n", "    )\n", "    return {\"documents\": documents, \"question\": question, \"steps\": steps}\n", "\n", "\n", "def decide_to_generate(state):\n", "    \"\"\"\n", "    Determines whether to generate an answer, or re-generate a question.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Binary decision for next node to call\n", "    \"\"\"\n", "    search = state[\"search\"]\n", "    if search == \"Yes\":\n", "        return \"search\"\n", "    else:\n", "        return \"generate\"\n", "\n", "\n", "# Graph\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"retrieve\", retrieve)  # retrieve\n", "workflow.add_node(\"grade_documents\", grade_documents)  # grade documents\n", "workflow.add_node(\"generate\", generate)  # generate\n", "workflow.add_node(\"web_search\", web_search)  # web search\n", "\n", "# Build graph\n", "workflow.add_edge(START, \"retrieve\")\n", "workflow.add_edge(\"retrieve\", \"grade_documents\")\n", "workflow.add_conditional_edges(\n", "    \"grade_documents\",\n", "    decide_to_generate,\n", "    {\n", "        \"search\": \"web_search\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"web_search\", \"generate\")\n", "workflow.add_edge(\"generate\", END)\n", "\n", "custom_graph = workflow.compile()\n", "\n", "display(Image(custom_graph.get_graph(xray=True).draw_mermaid_png()))"]}, {"cell_type": "code", "execution_count": 7, "id": "447d1333-082d-479a-a6fa-0ac0df78bb9d", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'response': 'According to the documents, there are two types of agent memory:\\n\\n* Short-term memory (STM): This is a data structure that holds information temporarily and allows the agent to process it when needed.\\n* Long-term memory (LTM): This provides the agent with the capability to retain and recall information over extended periods.\\n\\nThese types of memories allow the agent to learn, reason, and make decisions.',\n", " 'steps': ['retrieve_documents',\n", "  'grade_document_retrieval',\n", "  'web_search',\n", "  'generate_answer']}"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import uuid\n", "\n", "\n", "def predict_custom_agent_local_answer(example: dict):\n", "    config = {\"configurable\": {\"thread_id\": str(uuid.uuid4())}}\n", "    state_dict = custom_graph.invoke(\n", "        {\"question\": example[\"input\"], \"steps\": []}, config\n", "    )\n", "    return {\"response\": state_dict[\"generation\"], \"steps\": state_dict[\"steps\"]}\n", "\n", "\n", "example = {\"input\": \"What are the types of agent memory?\"}\n", "response = predict_custom_agent_local_answer(example)\n", "response"]}, {"cell_type": "markdown", "id": "91325c88-ec77-4c79-8a77-cb2e2842bcd4", "metadata": {}, "source": ["Trace: \n", "\n", "https://smith.langchain.com/public/88e7579e-2571-4cf6-98d2-1f9ce3359967/r"]}, {"cell_type": "markdown", "id": "1b80d5da-f698-40d2-a2fb-4eac89e35350", "metadata": {}, "source": ["## Evaluation\n", "\n", "Now we've defined two different agent architectures that do roughly the same thing!\n", "\n", "We can evaluate them. See our [conceptual guide](https://docs.smith.langchain.com/concepts/evaluation#agents) for context on agent evaluation.\n", "\n", "### Response\n", "\n", "First, we can assess how well [our agent performs on a set of question-answer pairs](https://docs.smith.langchain.com/tutorials/Developers/agents#response-evaluation).\n", "\n", "We'll create a dataset and save it in LangSmith."]}, {"cell_type": "code", "execution_count": 8, "id": "b83706ac-724b-46b1-9f08-66e6c4fac742", "metadata": {}, "outputs": [], "source": ["from langsmith import Client\n", "\n", "client = Client()\n", "\n", "# Create a dataset\n", "examples = [\n", "    (\n", "        \"How does the ReAct agent use self-reflection? \",\n", "        \"ReAct integrates reasoning and acting, performing actions - such tools like Wikipedia search API - and then observing / reasoning about the tool outputs.\",\n", "    ),\n", "    (\n", "        \"What are the types of biases that can arise with few-shot prompting?\",\n", "        \"The biases that can arise with few-shot prompting include (1) Majority label bias, (2) Recency bias, and (3) Common token bias.\",\n", "    ),\n", "    (\n", "        \"What are five types of adversarial attacks?\",\n", "        \"Five types of adversarial attacks are (1) Token manipulation, (2) Gradient based attack, (3) Jailbreak prompting, (4) Human red-teaming, (5) Model red-teaming.\",\n", "    ),\n", "    (\n", "        \"Who did the Chicago Bears draft first in the 2024 NFL draft”?\",\n", "        \"The Chicago Bears drafted <PERSON> first in the 2024 NFL draft.\",\n", "    ),\n", "    (\"Who won the 2024 NBA finals?\", \"The Boston Celtics on the 2024 NBA finals\"),\n", "]\n", "\n", "# Save it\n", "dataset_name = \"Corrective RAG Agent Testing\"\n", "if not client.has_dataset(dataset_name=dataset_name):\n", "    dataset = client.create_dataset(dataset_name=dataset_name)\n", "    inputs, outputs = zip(\n", "        *[({\"input\": text}, {\"output\": label}) for text, label in examples]\n", "    )\n", "    client.create_examples(inputs=inputs, outputs=outputs, dataset_id=dataset.id)"]}, {"cell_type": "markdown", "id": "a23f6bc0-2d03-488c-8f4b-747c93876788", "metadata": {}, "source": ["Now, we'll use an `LLM as a grader` to compare both agent responses to our ground truth reference answer.\n", "\n", "[Here](https://smith.langchain.com/hub/rlm/rag-answer-vs-reference) is the default prompt that we can use.\n", "\n", "We'll use `gpt-4o` as our LLM grader.\n"]}, {"cell_type": "code", "execution_count": 9, "id": "0a63776c-f9cd-46ce-b8cf-95c066dc5b06", "metadata": {}, "outputs": [], "source": ["from langchain import hub\n", "from langchain_openai import ChatOpenAI\n", "\n", "# Grade prompt\n", "grade_prompt_answer_accuracy = hub.pull(\"langchain-ai/rag-answer-vs-reference\")\n", "\n", "\n", "def answer_evaluator(run, example) -> dict:\n", "    \"\"\"\n", "    A simple evaluator for RAG answer accuracy\n", "    \"\"\"\n", "\n", "    # Get the question, the ground truth reference answer, RAG chain answer prediction\n", "    input_question = example.inputs[\"input\"]\n", "    reference = example.outputs[\"output\"]\n", "    prediction = run.outputs[\"response\"]\n", "\n", "    # Define an LLM grader\n", "    llm = ChatOpenAI(model=\"gpt-4o\", temperature=0)\n", "    answer_grader = grade_prompt_answer_accuracy | llm\n", "\n", "    # Run evaluator\n", "    score = answer_grader.invoke(\n", "        {\n", "            \"question\": input_question,\n", "            \"correct_answer\": reference,\n", "            \"student_answer\": prediction,\n", "        }\n", "    )\n", "    score = score[\"Score\"]\n", "    return {\"key\": \"answer_v_reference_score\", \"score\": score}"]}, {"cell_type": "markdown", "id": "960f1a01-7f8c-429f-83d0-052cea47b32b", "metadata": {}, "source": ["### Trajectory\n", "\n", "Second, [we can assess the list of tool calls](https://docs.smith.langchain.com/tutorials/Developers/agents#trajectory) that each agent makes relative to expected trajectories.\n", "\n", "This evaluates the specific reasoning traces taken by our agents!"]}, {"cell_type": "code", "execution_count": 10, "id": "deb28175-27a1-4afc-9747-2983e87fc881", "metadata": {}, "outputs": [], "source": ["from langsmith.schemas import Example, Run\n", "\n", "# Reasoning traces that we expect the agents to take\n", "expected_trajectory_1 = [\n", "    \"retrieve_documents\",\n", "    \"grade_document_retrieval\",\n", "    \"web_search\",\n", "    \"generate_answer\",\n", "]\n", "expected_trajectory_2 = [\n", "    \"retrieve_documents\",\n", "    \"grade_document_retrieval\",\n", "    \"generate_answer\",\n", "]\n", "\n", "\n", "def find_tool_calls_react(messages):\n", "    \"\"\"\n", "    Find all tool calls in the messages returned\n", "    \"\"\"\n", "    tool_calls = [\n", "        tc[\"name\"] for m in messages[\"messages\"] for tc in getattr(m, \"tool_calls\", [])\n", "    ]\n", "    return tool_calls\n", "\n", "\n", "def check_trajectory_react(root_run: Run, example: Example) -> dict:\n", "    \"\"\"\n", "    Check if all expected tools are called in exact order and without any additional tool calls.\n", "    \"\"\"\n", "    messages = root_run.outputs[\"messages\"]\n", "    tool_calls = find_tool_calls_react(messages)\n", "    print(f\"<PERSON><PERSON> calls ReAct agent: {tool_calls}\")\n", "    if tool_calls == expected_trajectory_1 or tool_calls == expected_trajectory_2:\n", "        score = 1\n", "    else:\n", "        score = 0\n", "\n", "    return {\"score\": int(score), \"key\": \"tool_calls_in_exact_order\"}\n", "\n", "\n", "def check_trajectory_custom(root_run: Run, example: Example) -> dict:\n", "    \"\"\"\n", "    Check if all expected tools are called in exact order and without any additional tool calls.\n", "    \"\"\"\n", "    tool_calls = root_run.outputs[\"steps\"]\n", "    print(f\"<PERSON><PERSON> calls custom agent: {tool_calls}\")\n", "    if tool_calls == expected_trajectory_1 or tool_calls == expected_trajectory_2:\n", "        score = 1\n", "    else:\n", "        score = 0\n", "\n", "    return {\"score\": int(score), \"key\": \"tool_calls_in_exact_order\"}"]}, {"cell_type": "code", "execution_count": 11, "id": "909b097d-cda1-45ff-8210-afeb2d18b8ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["View the evaluation results for experiment: 'custom-agent-llama3-8b-answer-and-tool-use-d6006159' at:\n", "https://smith.langchain.com/o/1fa8b1f4-fcb9-4072-9aa9-983e35ad61b8/datasets/a8b9273b-ca33-4e2f-9f69-9bbc37f6f51b/compare?selectedSessions=83c60822-ef22-43e8-ac85-4488af279c6f\n", "\n", "\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "529952314cd34ac1bb115840536921c3", "version_major": 2, "version_minor": 0}, "text/plain": ["0it [00:00, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n", "Tool calls custom agent: ['retrieve_documents', 'grade_document_retrieval', 'web_search', 'generate_answer']\n"]}], "source": ["from langsmith.evaluation import evaluate\n", "\n", "experiment_prefix = f\"custom-agent-{model_tested}\"\n", "experiment_results = evaluate(\n", "    predict_custom_agent_local_answer,\n", "    data=dataset_name,\n", "    evaluators=[answer_evaluator, check_trajectory_custom],\n", "    experiment_prefix=experiment_prefix + \"-answer-and-tool-use\",\n", "    num_repetitions=3,\n", "    max_concurrency=1,  # Use when running locally\n", "    metadata={\"version\": metadata},\n", ")"]}, {"attachments": {"80e86604-7734-4aeb-a200-d1413870c3cb.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "47c38cd3-2c31-48c4-8281-bd9e1f7b2830", "metadata": {}, "source": ["We can see the results benchmarked against `GPT-4o` and `Llama-3-70b` using `Custom` agent (as shown here) and ReAct.\n", "\n", "![Screenshot 2024-06-24 at 4.14.04 PM.png](attachment:80e86604-7734-4aeb-a200-d1413870c3cb.png)\n", "\n", "The `local custom agent` performs well in terms of tool calling reliability: it follows the expected reasoning traces.\n", "\n", "However, the answer accuracy performance lags the larger models with `custom agent` implementations."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}