# 数学计算系统演示脚本
import math_mcp_server

def demo_mcp_functions():
    """演示MCP数学工具功能"""
    print("🧮 数学计算系统演示")
    print("=" * 50)
    
    print("\n📊 基础运算演示:")
    print("-" * 30)
    
    # 加法
    result = math_mcp_server.add(15, 27)
    print(f"15 + 27 = {result}")
    
    # 减法
    result = math_mcp_server.subtract(100, 45)
    print(f"100 - 45 = {result}")
    
    # 乘法
    result = math_mcp_server.multiply(8, 9)
    print(f"8 × 9 = {result}")
    
    # 除法
    result = math_mcp_server.divide(144, 12)
    print(f"144 ÷ 12 = {result}")
    
    print("\n🔬 高级运算演示:")
    print("-" * 30)
    
    # 幂运算
    result = math_mcp_server.power(2, 8)
    print(f"2^8 = {result}")
    
    # 平方根
    result = math_mcp_server.square_root(144)
    print(f"√144 = {result}")
    
    # 阶乘
    result = math_mcp_server.factorial(5)
    print(f"5! = {result}")
    
    # 百分比
    result = math_mcp_server.percentage(25, 200)
    print(f"25占200的百分比 = {result}%")
    
    print("\n📐 方程求解演示:")
    print("-" * 30)
    
    # 一元一次方程
    result = math_mcp_server.solve_linear_equation(3, 6)
    print(f"解方程 3x + 6 = 0: x = {result}")
    
    print("\n🧮 表达式计算演示:")
    print("-" * 30)
    
    # 复杂表达式
    result = math_mcp_server.calculate_expression("(2 + 3) * 4 - 1")
    print(f"(2 + 3) × 4 - 1 = {result}")
    
    print("\n✅ 所有功能演示完成！")

def demo_error_handling():
    """演示错误处理"""
    print("\n⚠️  错误处理演示:")
    print("-" * 30)
    
    # 除零错误
    try:
        math_mcp_server.divide(10, 0)
    except ValueError as e:
        print(f"除零错误: {e}")
    
    # 负数平方根错误
    try:
        math_mcp_server.square_root(-1)
    except ValueError as e:
        print(f"负数平方根错误: {e}")
    
    # 负数阶乘错误
    try:
        math_mcp_server.factorial(-1)
    except ValueError as e:
        print(f"负数阶乘错误: {e}")

def show_system_info():
    """显示系统信息"""
    print("\n📋 系统信息:")
    print("-" * 30)
    print("🏗️  技术架构:")
    print("   • MCP协议: 自定义数学工具")
    print("   • LangGraph: 智能工作流编排")
    print("   • FastAPI: 高性能Web后端")
    print("   • Bootstrap: 响应式前端界面")
    
    print("\n🔧 可用工具:")
    tools = [
        "add - 加法运算",
        "subtract - 减法运算", 
        "multiply - 乘法运算",
        "divide - 除法运算",
        "power - 幂运算",
        "square_root - 平方根",
        "factorial - 阶乘",
        "percentage - 百分比计算",
        "solve_linear_equation - 方程求解",
        "calculate_expression - 表达式计算"
    ]
    
    for tool in tools:
        print(f"   • {tool}")
    
    print("\n🚀 启动完整系统:")
    print("   python start_system.py")
    print("   然后访问: http://127.0.0.1:8000")

if __name__ == "__main__":
    try:
        # 演示MCP功能
        demo_mcp_functions()
        
        # 演示错误处理
        demo_error_handling()
        
        # 显示系统信息
        show_system_info()
        
        print("\n🎉 演示完成！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        print("请检查依赖是否正确安装")
