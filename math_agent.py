# 数学计算智能代理 - LangGraph工作流
import os
import asyncio
from typing import Literal, Dict, Any
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, StateGraph, MessagesState
from langgraph.prebuilt import ToolN<PERSON>
from langchain_mcp_adapters.client import MultiServerMCPClient
from config import MODEL_CONFIG, LANGSMITH_CONFIG, MCP_SERVERS

# 设置 LangSmith 环境变量（可选）
if LANGSMITH_CONFIG.get("api_key"):
    os.environ["LANGCHAIN_TRACING_V2"] = LANGSMITH_CONFIG["tracing_v2"]
    os.environ["LANGCHAIN_API_KEY"] = LANGSMITH_CONFIG["api_key"]
    os.environ["LANGCHAIN_ENDPOINT"] = LANGSMITH_CONFIG["endpoint"]
    os.environ["LANGCHAIN_PROJECT"] = LANGSMITH_CONFIG["project"]

class MathCalculatorAgent:
    def __init__(self):
        self.client = None
        self.mcp_tools = []
        self.app = None
        self._initialize_agent()
    
    def _initialize_agent(self):
        """初始化数学计算代理"""
        try:
            # 初始化MCP客户端和工具
            self.client = MultiServerMCPClient(MCP_SERVERS)
            self.mcp_tools = asyncio.run(self.client.get_tools())
            print(f"成功加载 {len(self.mcp_tools)} 个MCP数学工具")
        except Exception as e:
            print(f"MCP工具加载失败: {e}")
            self.mcp_tools = []
        
        # 定义额外的工具函数
        @tool
        def get_calculation_help() -> str:
            """获取数学计算帮助信息"""
            return """
            我是数学计算助手，可以帮您进行以下运算：
            1. 基础运算：加法(+)、减法(-)、乘法(×)、除法(÷)
            2. 高级运算：幂运算(^)、平方根(√)、阶乘(!)
            3. 方程求解：一元一次方程求解
            4. 百分比计算
            5. 复杂表达式计算
            
            请告诉我您想要计算什么，我会为您提供准确的结果！
            """
        
        # 将工具函数放入工具列表
        tools = [get_calculation_help] + self.mcp_tools
        
        # 创建工具节点
        tool_node = ToolNode(tools)
        
        # 初始化模型并绑定工具
        model = ChatOpenAI(
            model=MODEL_CONFIG["model_name"],
            openai_api_base=MODEL_CONFIG["base_url"],
            openai_api_key=MODEL_CONFIG["api_key"],
            temperature=MODEL_CONFIG["temperature"]
        ).bind_tools(tools)
        
        # 定义函数，决定是否继续执行
        def should_continue(state: MessagesState) -> Literal["tools", END]:
            messages = state["messages"]
            last_message = messages[-1]
            # 如果LLM调用了工具，则转到 tools 节点
            if last_message.tool_calls:
                return "tools"
            # 否则，停止，回复用户
            return END
        
        # 定义调用模型的函数
        async def call_model(state: MessagesState):
            messages = state["messages"]
            
            # 添加系统提示
            system_prompt = """你是一个专业的数学计算助手。你的任务是：
            1. 理解用户的数学问题
            2. 选择合适的数学工具进行计算
            3. 提供清晰、准确的计算结果
            4. 如果需要，解释计算过程
            
            当用户提出数学问题时，请：
            - 分析问题类型
            - 调用相应的数学工具
            - 返回计算结果和必要的解释
            
            可用的数学工具包括：加法、减法、乘法、除法、幂运算、平方根、阶乘、百分比计算、方程求解等。
            """
            
            # 如果第一条消息不是系统消息，则添加系统提示
            if not messages or messages[0].type != "system":
                from langchain_core.messages import SystemMessage
                messages = [SystemMessage(content=system_prompt)] + messages
            
            response = await model.ainvoke(messages)
            return {"messages": [response]}
        
        # 初始化状态图
        workflow = StateGraph(MessagesState)
        
        # 定义图节点
        workflow.add_node("agent", call_model)
        workflow.add_node("tools", tool_node)
        
        # 定义入口节点和边
        workflow.set_entry_point("agent")
        workflow.add_conditional_edges("agent", should_continue)
        workflow.add_edge("tools", "agent")
        
        # 初始化内存
        checkpointer = MemorySaver()
        
        # 编译图
        self.app = workflow.compile(checkpointer=checkpointer)
        print("数学计算代理初始化完成")
    
    async def calculate(self, question: str, thread_id: str = "default") -> Dict[str, Any]:
        """
        执行数学计算
        
        Args:
            question: 数学问题
            thread_id: 会话ID，用于保持上下文
        
        Returns:
            包含计算结果的字典
        """
        try:
            final_state = await self.app.ainvoke(
                {"messages": [HumanMessage(content=question)]},
                config={"configurable": {"thread_id": thread_id}}
            )
            
            # 获取最后一条消息的内容
            result = final_state["messages"][-1].content
            
            return {
                "success": True,
                "result": result,
                "question": question,
                "thread_id": thread_id
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "question": question,
                "thread_id": thread_id
            }
    
    async def get_conversation_history(self, thread_id: str = "default") -> list:
        """获取会话历史"""
        try:
            # 这里可以实现获取会话历史的逻辑
            # 由于MemorySaver的限制，这里返回空列表
            return []
        except Exception as e:
            print(f"获取会话历史失败: {e}")
            return []

# 创建全局代理实例
math_agent = MathCalculatorAgent()

async def main():
    """测试数学计算代理"""
    test_questions = [
        "计算 15 + 27 等于多少？",
        "求 144 的平方根",
        "计算 5 的阶乘",
        "解方程 3x + 6 = 0",
        "计算 25 占 200 的百分比",
        "计算表达式 (2 + 3) * 4 - 1"
    ]
    
    print("=== 数学计算代理测试 ===")
    for i, question in enumerate(test_questions, 1):
        print(f"\n测试 {i}: {question}")
        result = await math_agent.calculate(question, f"test_{i}")
        
        if result["success"]:
            print(f"结果: {result['result']}")
        else:
            print(f"错误: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
