# 数学计算系统 (Math Calculator System)

基于 MCP 协议和 LangGraph 的智能数学计算系统，提供完整的 (a+b=c) 数学运算解决方案。

## 🎯 项目特性

- **🔧 自定义MCP协议工具**: 实现基础和高级数学运算
- **🤖 LangGraph工作流集成**: 智能数学计算代理
- **🌐 网页交互界面**: 用户友好的前端界面
- **⚡ 高性能后端**: FastAPI + Pydantic 数据验证
- **📱 响应式设计**: 支持桌面和移动设备

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   FastAPI后端   │    │   MCP数学工具   │
│  HTML/JS/CSS    │◄──►│   main.py       │◄──►│math_mcp_server.py│
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │  LangGraph代理  │
                       │  math_agent.py  │
                       └─────────────────┘
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.10+
- OpenAI API Key (或兼容的API)

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境

设置 OpenAI API Key:
```bash
export OPENAI_API_KEY="your-api-key-here"
```

或在 `config.py` 中直接配置。

### 4. 启动系统

```bash
python start_system.py
```

### 5. 访问界面

打开浏览器访问: http://127.0.0.1:8000

## 📊 功能列表

### 基础运算
- ➕ **加法**: `计算 15 + 27`
- ➖ **减法**: `计算 100 - 45`
- ✖️ **乘法**: `计算 8 × 9`
- ➗ **除法**: `计算 144 ÷ 12`

### 高级运算
- 🔢 **幂运算**: `计算 2 的 8 次方`
- √ **平方根**: `求 144 的平方根`
- ❗ **阶乘**: `计算 5 的阶乘`
- 📊 **百分比**: `计算 25 占 200 的百分比`

### 方程求解
- 📐 **一元一次方程**: `解方程 3x + 6 = 0`

### 表达式计算
- 🧮 **复杂表达式**: `计算 (2 + 3) × 4 - 1`

## 📁 项目结构

```
math-calculator-system/
├── main.py                 # FastAPI 后端服务
├── math_agent.py          # LangGraph 数学代理
├── math_mcp_server.py     # MCP 数学工具服务器
├── config.py              # 配置文件
├── start_system.py        # 系统启动脚本
├── test_system.py         # 测试文件
├── requirements.txt       # 依赖包列表
├── README.md             # 项目文档
├── templates/
│   └── index.html        # 主页模板
└── static/
    ├── style.css         # 样式文件
    └── app.js           # 前端 JavaScript
```

## 🔧 API 接口

### 计算接口
```http
POST /api/calculate
Content-Type: application/json

{
    "question": "计算 15 + 27",
    "thread_id": "optional-session-id"
}
```

### 响应格式
```json
{
    "success": true,
    "result": "15 + 27 = 42",
    "question": "计算 15 + 27",
    "thread_id": "session_xxx"
}
```

### 其他接口
- `GET /health` - 健康检查
- `GET /api/tools` - 获取可用工具
- `GET /api/examples` - 获取计算示例
- `GET /api/sessions` - 获取会话信息

## 🧪 测试

### 运行所有测试
```bash
python test_system.py
```

### 运行特定测试
```bash
# 使用 pytest
pip install pytest pytest-asyncio
pytest test_system.py -v
```

## ⚙️ 配置说明

### config.py 配置项

```python
# 模型配置
MODEL_CONFIG = {
    "model_name": "gpt-4o-mini",
    "base_url": "https://api.openai.com/v1",
    "api_key": "your-api-key",
    "temperature": 0.1
}

# FastAPI 配置
FASTAPI_CONFIG = {
    "host": "127.0.0.1",
    "port": 8000,
    "reload": True
}
```

## 🔍 MCP 工具详情

| 工具名称 | 功能描述 | 参数 | 示例 |
|---------|---------|------|------|
| `add` | 加法运算 | a, b | `add(15, 27)` |
| `subtract` | 减法运算 | a, b | `subtract(100, 45)` |
| `multiply` | 乘法运算 | a, b | `multiply(8, 9)` |
| `divide` | 除法运算 | a, b | `divide(144, 12)` |
| `power` | 幂运算 | a, b | `power(2, 8)` |
| `square_root` | 平方根 | a | `square_root(144)` |
| `factorial` | 阶乘 | n | `factorial(5)` |
| `percentage` | 百分比 | a, b | `percentage(25, 200)` |
| `solve_linear_equation` | 方程求解 | a, b | `solve_linear_equation(3, 6)` |
| `calculate_expression` | 表达式计算 | expression | `calculate_expression("2+3*4")` |

## 🐛 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

2. **API Key 错误**
   - 检查环境变量设置
   - 确认 API Key 有效性
   - 检查 config.py 配置

3. **端口占用**
   - 修改 config.py 中的端口号
   - 或停止占用端口的进程

4. **MCP 工具加载失败**
   - 检查 mcp 包是否正确安装
   - 确认 math_mcp_server.py 文件存在

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [LangGraph](https://github.com/langchain-ai/langgraph) - 工作流编排框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代 Web 框架
- [MCP](https://modelcontextprotocol.io/) - 模型上下文协议
- [Bootstrap](https://getbootstrap.com/) - 前端 UI 框架

## 📞 联系方式

如有问题或建议，请通过以下方式联系:

- 创建 [Issue](https://github.com/your-repo/issues)
- 发送邮件至: <EMAIL>

---

**享受数学计算的乐趣！** 🎉
