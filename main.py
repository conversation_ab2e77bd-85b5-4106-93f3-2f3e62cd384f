# FastAPI 后端服务 - 数学计算系统
from fastapi import FastAPI, HTTPException, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any
import asyncio
import uuid
from math_agent import math_agent
from config import FASTAPI_CONFIG

# 创建FastAPI应用
app = FastAPI(
    title="数学计算系统",
    description="基于MCP协议和LangGraph的智能数学计算系统",
    version="1.0.0"
)

# 配置静态文件和模板
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# 数据模型
class CalculationRequest(BaseModel):
    question: str
    thread_id: Optional[str] = None

class CalculationResponse(BaseModel):
    success: bool
    result: Optional[str] = None
    error: Optional[str] = None
    question: str
    thread_id: str

class HealthResponse(BaseModel):
    status: str
    message: str

# 存储会话信息
sessions: Dict[str, Dict[str, Any]] = {}

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """主页"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查"""
    return HealthResponse(
        status="healthy",
        message="数学计算系统运行正常"
    )

@app.post("/api/calculate", response_model=CalculationResponse)
async def calculate(request: CalculationRequest):
    """
    数学计算API
    
    Args:
        request: 包含数学问题和可选会话ID的请求
    
    Returns:
        计算结果
    """
    try:
        # 生成或使用现有的会话ID
        thread_id = request.thread_id or str(uuid.uuid4())
        
        # 调用数学代理进行计算
        result = await math_agent.calculate(request.question, thread_id)
        
        # 更新会话信息
        if thread_id not in sessions:
            sessions[thread_id] = {
                "created_at": asyncio.get_event_loop().time(),
                "question_count": 0,
                "last_activity": asyncio.get_event_loop().time()
            }
        
        sessions[thread_id]["question_count"] += 1
        sessions[thread_id]["last_activity"] = asyncio.get_event_loop().time()
        
        return CalculationResponse(
            success=result["success"],
            result=result.get("result"),
            error=result.get("error"),
            question=request.question,
            thread_id=thread_id
        )
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算过程中发生错误: {str(e)}")

@app.get("/api/sessions")
async def get_sessions():
    """获取所有会话信息"""
    return {
        "sessions": sessions,
        "total_sessions": len(sessions)
    }

@app.get("/api/sessions/{thread_id}")
async def get_session(thread_id: str):
    """获取特定会话信息"""
    if thread_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    return {
        "thread_id": thread_id,
        "session_info": sessions[thread_id]
    }

@app.delete("/api/sessions/{thread_id}")
async def delete_session(thread_id: str):
    """删除特定会话"""
    if thread_id not in sessions:
        raise HTTPException(status_code=404, detail="会话不存在")
    
    del sessions[thread_id]
    return {"message": f"会话 {thread_id} 已删除"}

@app.get("/api/tools")
async def get_available_tools():
    """获取可用的数学工具列表"""
    tools_info = [
        {
            "name": "add",
            "description": "计算两个数的和 (a + b = c)",
            "parameters": ["a: 第一个数字", "b: 第二个数字"]
        },
        {
            "name": "subtract", 
            "description": "计算两个数的差 (a - b = c)",
            "parameters": ["a: 被减数", "b: 减数"]
        },
        {
            "name": "multiply",
            "description": "计算两个数的积 (a × b = c)",
            "parameters": ["a: 第一个数字", "b: 第二个数字"]
        },
        {
            "name": "divide",
            "description": "计算两个数的商 (a ÷ b = c)",
            "parameters": ["a: 被除数", "b: 除数"]
        },
        {
            "name": "power",
            "description": "计算a的b次方 (a^b = c)",
            "parameters": ["a: 底数", "b: 指数"]
        },
        {
            "name": "square_root",
            "description": "计算数字的平方根 (√a = c)",
            "parameters": ["a: 要计算平方根的数字"]
        },
        {
            "name": "percentage",
            "description": "计算a占b的百分比 (a/b × 100% = c%)",
            "parameters": ["a: 部分数值", "b: 总数值"]
        },
        {
            "name": "factorial",
            "description": "计算n的阶乘 (n! = c)",
            "parameters": ["n: 要计算阶乘的非负整数"]
        },
        {
            "name": "solve_linear_equation",
            "description": "解一元一次方程 ax + b = 0",
            "parameters": ["a: x的系数", "b: 常数项"]
        },
        {
            "name": "calculate_expression",
            "description": "计算数学表达式的值",
            "parameters": ["expression: 数学表达式字符串"]
        }
    ]
    
    return {
        "tools": tools_info,
        "total_tools": len(tools_info)
    }

@app.get("/api/examples")
async def get_calculation_examples():
    """获取计算示例"""
    examples = [
        {
            "category": "基础运算",
            "examples": [
                "计算 15 + 27",
                "求 100 - 45",
                "计算 8 × 9",
                "求 144 ÷ 12"
            ]
        },
        {
            "category": "高级运算",
            "examples": [
                "计算 2 的 8 次方",
                "求 144 的平方根",
                "计算 5 的阶乘",
                "计算 25 占 200 的百分比"
            ]
        },
        {
            "category": "方程求解",
            "examples": [
                "解方程 3x + 6 = 0",
                "解方程 2x - 10 = 0",
                "解方程 -5x + 15 = 0"
            ]
        },
        {
            "category": "复杂表达式",
            "examples": [
                "计算 (2 + 3) × 4 - 1",
                "计算 10 + 5 × 2 - 3",
                "计算 (15 + 5) ÷ 4 + 2"
            ]
        }
    ]
    
    return {"examples": examples}

if __name__ == "__main__":
    import uvicorn
    print("启动数学计算系统...")
    print(f"访问地址: http://{FASTAPI_CONFIG['host']}:{FASTAPI_CONFIG['port']}")
    
    uvicorn.run(
        "main:app",
        host=FASTAPI_CONFIG["host"],
        port=FASTAPI_CONFIG["port"],
        reload=FASTAPI_CONFIG["reload"]
    )
