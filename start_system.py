# 数学计算系统启动脚本
import os
import sys
import subprocess
import time
import asyncio
from pathlib import Path

def check_dependencies():
    """检查依赖包是否安装"""
    print("检查系统依赖...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'langgraph',
        'langchain',
        'langchain-openai',
        'langchain-core',
        'langchain-mcp-adapters',
        'mcp'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_environment():
    """检查环境配置"""
    print("\n检查环境配置...")
    
    # 检查OpenAI API Key
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key or api_key == 'your-api-key-here':
        print("⚠️  未设置 OPENAI_API_KEY 环境变量")
        print("请设置您的OpenAI API密钥:")
        print("export OPENAI_API_KEY='your-actual-api-key'")
        print("或在 config.py 中直接配置")
    else:
        print("✅ OPENAI_API_KEY 已设置")
    
    # 检查必要文件
    required_files = [
        'main.py',
        'math_agent.py',
        'math_mcp_server.py',
        'config.py',
        'templates/index.html',
        'static/style.css',
        'static/app.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            print(f"❌ {file_path} - 文件不存在")
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n缺少以下文件: {', '.join(missing_files)}")
        return False
    
    return True

async def test_mcp_server():
    """测试MCP服务器"""
    print("\n测试MCP服务器...")
    
    try:
        # 导入并测试MCP服务器函数
        import math_mcp_server
        
        # 测试基本功能
        test_cases = [
            (math_mcp_server.add, [10, 20], 30),
            (math_mcp_server.subtract, [50, 30], 20),
            (math_mcp_server.multiply, [6, 7], 42),
            (math_mcp_server.divide, [100, 5], 20)
        ]
        
        for func, args, expected in test_cases:
            result = func(*args)
            if result == expected:
                print(f"✅ {func.__name__}({', '.join(map(str, args))}) = {result}")
            else:
                print(f"❌ {func.__name__}({', '.join(map(str, args))}) = {result}, 期望 {expected}")
                return False
        
        print("✅ MCP服务器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ MCP服务器测试失败: {e}")
        return False

async def test_math_agent():
    """测试数学代理"""
    print("\n测试数学代理...")
    
    try:
        from math_agent import MathCalculatorAgent
        
        agent = MathCalculatorAgent()
        
        # 简单测试
        result = await agent.calculate("计算 2 + 3", "startup_test")
        
        if result["success"]:
            print("✅ 数学代理测试通过")
            print(f"测试结果: {result['result']}")
            return True
        else:
            print(f"❌ 数学代理测试失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 数学代理测试失败: {e}")
        return False

def start_web_server():
    """启动Web服务器"""
    print("\n启动Web服务器...")
    
    try:
        # 导入配置
        from config import FASTAPI_CONFIG
        
        host = FASTAPI_CONFIG["host"]
        port = FASTAPI_CONFIG["port"]
        
        print(f"服务器地址: http://{host}:{port}")
        print("按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        # 启动服务器
        import uvicorn
        uvicorn.run(
            "main:app",
            host=host,
            port=port,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")

def show_usage_info():
    """显示使用说明"""
    print("\n" + "="*60)
    print("🎉 数学计算系统启动成功！")
    print("="*60)
    print("\n📖 使用说明:")
    print("1. 在浏览器中访问 http://127.0.0.1:8000")
    print("2. 在输入框中输入数学问题，例如:")
    print("   - 计算 15 + 27")
    print("   - 求 144 的平方根")
    print("   - 解方程 3x + 6 = 0")
    print("   - 计算 (2 + 3) × 4 - 1")
    print("\n🔧 功能特性:")
    print("- 基础运算: 加减乘除")
    print("- 高级运算: 幂运算、平方根、阶乘")
    print("- 方程求解: 一元一次方程")
    print("- 百分比计算")
    print("- 复杂表达式计算")
    print("\n🛠️ 技术架构:")
    print("- MCP协议: 自定义数学工具")
    print("- LangGraph: 智能工作流编排")
    print("- FastAPI: 高性能Web后端")
    print("- Bootstrap: 响应式前端界面")
    print("\n按 Ctrl+C 停止服务器")
    print("="*60)

async def main():
    """主函数"""
    print("🚀 启动数学计算系统")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装缺少的包")
        return
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请检查配置")
        return
    
    # 测试MCP服务器
    if not await test_mcp_server():
        print("\n❌ MCP服务器测试失败")
        return
    
    # 测试数学代理
    if not await test_math_agent():
        print("\n❌ 数学代理测试失败")
        return
    
    # 显示使用说明
    show_usage_info()
    
    # 启动Web服务器
    start_web_server()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 感谢使用数学计算系统！")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        print("请检查配置和依赖是否正确安装")
