from qwen_agent.tools.base import BaseTool, register_tool
import matplotlib.pyplot as plt
import matplotlib
import os
import json
from matplotlib import font_manager

# 设置中文字体，解决中文显示成方块的问题
font_path = "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc"
my_font = font_manager.FontProperties(fname=font_path)
# matplotlib.rcParams['font.family'] = my_font.get_name() # 全局设置字体不起作用，下面改用局部指定
# 解决负号 '-' 显示成方块的问题
matplotlib.rcParams['axes.unicode_minus'] = False

matplotlib.use('Agg')

@register_tool("data_visualization")
class PlotTool(BaseTool):
  description = "绘制折线图"
  # parameters作用：为此工具定义参数的格式、类型和用途，让qwen-agent能够在调用这个工具时自动知道如何传参，以及为后续的LLM推理提供必要信息
  parameters = [
    {
      "name": "data", # 参数名称，调用时需要传入的键
      "type": "list", # 参数类型，规定必须是 list （通常是 list of numbers）
      "description": "数据列表", # 参数描述，帮助LLM理解作用
      "required": True # 是否必须传入
    }
  ]
  def call(self, data: list, **kwargs):
    if isinstance(data, str):
      try:
        data = json.loads(data) # qwen-agent传递进来的data是一个json字符串，如：'{"data": [10, 8, 100, 4]}'，所以需要先转成json
      except Exception:
        return "数据格式错误，无法解析JSON，尝试传递JSON字符串"

    if isinstance(data, dict) and "data" in data:
      data = data["data"] # 取出 data.data 数据

    print("data_visualization data:", type(data), data)

    if not data or not all(isinstance(x, (int, float)) for x in data):
      return "无效数据：请提供数字列表。"

    fig, ax = plt.subplots()  # 使用显式 figure，明确创建一个新画布
    ax.plot(data)
    ax.set_title("折线图示例", fontproperties=my_font) # 局部显示指定字体
    ax.set_xlabel("索引", fontproperties=my_font)
    ax.set_ylabel("值", fontproperties=my_font)

    # 保存到绝对路径，确保路径正确
    save_path = os.path.abspath("plot.png")
    fig.savefig(save_path)
    plt.close(fig)  # 关闭当前 fig，释放内存，避免多次绘图污染

    return f"图已保存 {save_path}"