name: PR Title Lint

permissions:
  pull-requests: read

on:
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  lint-pr-title:
    runs-on: ubuntu-latest
    steps:
      - name: Validate PR Title
        uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            build
            ci
            chore
            revert
            release
          scopes: |
            checkpoint
            checkpoint-postgres
            checkpoint-sqlite
            cli
            langgraph
            prebuilt
            scheduler-kafka
            sdk-py
            docs
            ci
          requireScope: false
          ignoreLabels: |
            ignore-lint-pr-title
