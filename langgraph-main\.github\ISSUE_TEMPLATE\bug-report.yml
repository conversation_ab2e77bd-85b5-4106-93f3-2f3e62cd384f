name: "\U0001F41B Bug Report"
description: Report a bug in LangGraph. To report a security issue, please instead use the security option below. For questions, please use the LangChain Forum at forum.langchain.com.
labels: [pending,bug]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to file a bug report. 
        
        Use this to report BUGS in LangGraph. For usage questions, feature requests and general design questions, please use the [LangChain Forum](https://forum.langchain.com/).
        
        Relevant links to check before filing a bug report to see if your issue has already been reported, fixed or
        if there's another way to solve your problem:
        
        * [LangChain Forum](https://forum.langchain.com/),
        * [LangGraph Github Issues](https://github.com/langchain-ai/langgraph/issues),
        * [LangGraph how-to guides](https://langchain-ai.github.io/langgraph/how-tos/).
        * [LangChain documentation with the integrated search](https://python.langchain.com/docs/get_started/introduction),
        * [GitHub search](https://github.com/langchain-ai/langgraph),
  - type: checkboxes
    id: checks
    attributes:
      label: Checked other resources
      description: Before submitting this issue, please confirm that you have completed all the steps below by checking each option. These steps help ensure your issue is well-defined, relevant, and actionable.
      options:
        - label: This is a bug, not a usage question. For questions, please use the LangChain Forum (https://forum.langchain.com/).
          required: true
        - label: I added a clear and detailed title that summarizes the issue.
          required: true
        - label: I read what a minimal reproducible example is (https://stackoverflow.com/help/minimal-reproducible-example).
          required: true
        - label: I included a self-contained, minimal example that demonstrates the issue INCLUDING all the relevant imports. The code run AS IS to reproduce the issue.
          required: true
  - type: textarea
    id: reproduction
    validations:
      required: true
    attributes:
      label: Example Code
      description: |
        Please add a self-contained, [minimal, reproducible, example](https://stackoverflow.com/help/minimal-reproducible-example) with your use case. Replace this code with your own!
      placeholder: |
        from langgraph.graph import StateGraph

        def bad_code(inputs) -> int:
          raise NotImplementedError('For demo purpose')
          
        chain = StateGraph(list)
        chain.invoke('Hello!')
      render: python
  - type: textarea
    id: error
    validations:
      required: false
    attributes:
      label: Error Message and Stack Trace (if applicable)
      description: |
        If you are reporting an error, please include the full error message and stack trace.
      placeholder: |
        Exception + full stack trace
      render: shell
  - type: textarea
    id: description
    attributes:
      label: Description
      description: |
        What is the problem, question, or error?

        Write a short description telling what you are doing, what you expect to happen, and what is currently happening.
      placeholder: |
        * I'm trying to use the `langgraph` library to do X.
        * I expect to see Y.
        * Instead, it does Z.
    validations:
      required: true
  - type: textarea
    id: system-info
    attributes:
      label: System Info
      description: |
        Run on your machine: `python -m langchain_core.sys_info`
      placeholder: |
        python -m langchain_core.sys_info
    validations:
      required: true
