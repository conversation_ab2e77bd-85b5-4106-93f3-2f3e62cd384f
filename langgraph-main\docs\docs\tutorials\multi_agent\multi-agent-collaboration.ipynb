{"cells": [{"attachments": {"8088306a-da20-4f95-bb07-c3fbd546762c.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "39fd1948-b5c3-48c4-b10e-2ae7e8c83334", "metadata": {}, "source": ["# Multi-agent network\n", "\n", "A single agent can usually operate effectively using a handful of tools within a single domain, but even using powerful models like `gpt-4`, it can be less effective at using many tools. \n", "\n", "One way to approach complicated tasks is through a \"divide-and-conquer\" approach: create a specialized agent for each task or domain and route tasks to the correct \"expert\". This is an example of a [multi-agent network](https://langchain-ai.github.io/langgraph/concepts/multi_agent/#network) architecture.\n", "\n", "This notebook (inspired by the paper [AutoGen: Enabling Next-Gen LLM Applications via Multi-Agent Conversation](https://arxiv.org/abs/2308.08155), by <PERSON>, et. al.) shows one way to do this using LangGraph.\n", "\n", "The resulting graph will look something like the following diagram:\n", "\n", "![multi_agent diagram](attachment:8088306a-da20-4f95-bb07-c3fbd546762c.png)\n", "\n", "Before we get started, a quick note: this and other multi-agent notebooks are designed to show _how_ you can implement certain design patterns in LangGraph. If the pattern suits your needs, we recommend combining it with some of the other fundamental patterns described elsewhere in the docs for best performance.\n", "\n", "## Setup\n", "\n", "First, let's install our required packages and set our API keys:"]}, {"cell_type": "code", "execution_count": 1, "id": "0d7b6dcc-c985-46e2-8457-7e6b0298b950", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install -U langchain_community langchain_anthropic langchain-tavily langchain_experimental matplotlib langgraph"]}, {"cell_type": "code", "execution_count": null, "id": "743c19df-6da9-4d1e-b2d2-ea40080b9fdc", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_if_undefined(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"Please provide your {var}\")\n", "\n", "\n", "_set_if_undefined(\"ANTHROPIC_API_KEY\")\n", "_set_if_undefined(\"TAVILY_API_KEY\")"]}, {"cell_type": "markdown", "id": "ab5cea6d", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "b4b40de2-5dd4-4d5b-882e-577210723ff4", "metadata": {}, "source": ["## Define tools\n", "\n", "We will also define some tools that our agents will use in the future"]}, {"cell_type": "code", "execution_count": 3, "id": "ca076f3b-a729-4ca9-8f91-05c2ba58d610", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "\n", "from langchain_tavily import TavilySearch\n", "from langchain_core.tools import tool\n", "from langchain_experimental.utilities import PythonREPL\n", "\n", "tavily_tool = TavilySearch(max_results=5)\n", "\n", "# Warning: This executes code locally, which can be unsafe when not sandboxed\n", "\n", "repl = PythonREPL()\n", "\n", "\n", "@tool\n", "def python_repl_tool(\n", "    code: Annotated[str, \"The python code to execute to generate your chart.\"],\n", "):\n", "    \"\"\"Use this to execute python code. If you want to see the output of a value,\n", "    you should print it out with `print(...)`. This is visible to the user.\"\"\"\n", "    try:\n", "        result = repl.run(code)\n", "    except BaseException as e:\n", "        return f\"Failed to execute. Error: {repr(e)}\"\n", "    result_str = f\"Successfully executed:\\n```python\\n{code}\\n```\\nStdout: {result}\"\n", "    return (\n", "        result_str + \"\\n\\nIf you have completed all tasks, respond with FINAL ANSWER.\"\n", "    )"]}, {"cell_type": "markdown", "id": "f1b54c0c-0b09-408b-abc5-86308929afb6", "metadata": {}, "source": ["## Create graph\n", "\n", "Now that we've defined our tools and made some helper functions, will create the individual agents below and tell them how to talk to each other using LangGraph."]}, {"cell_type": "markdown", "id": "911a283e-ea04-40c1-b792-f9e5f7d81203", "metadata": {}, "source": ["### Define Agent Nodes\n", "\n", "We now need to define the nodes.\n", "\n", "First, we'll create a utility to create a system prompt for each agent."]}, {"cell_type": "code", "execution_count": 4, "id": "4325a10e-38dc-4a98-9004-e1525eaba377", "metadata": {}, "outputs": [], "source": ["def make_system_prompt(suffix: str) -> str:\n", "    return (\n", "        \"You are a helpful AI assistant, collaborating with other assistants.\"\n", "        \" Use the provided tools to progress towards answering the question.\"\n", "        \" If you are unable to fully answer, that's OK, another assistant with different tools \"\n", "        \" will help where you left off. Execute what you can to make progress.\"\n", "        \" If you or any of the other assistants have the final answer or deliverable,\"\n", "        \" prefix your response with FINAL ANSWER so the team knows to stop.\"\n", "        f\"\\n{suffix}\"\n", "    )"]}, {"cell_type": "code", "execution_count": 7, "id": "71b790ca-9cef-4b22-b469-4b1d5d8424d6", "metadata": {}, "outputs": [], "source": ["from typing import Literal\n", "\n", "from langchain_core.messages import BaseMessage, HumanMessage\n", "from langchain_anthropic import ChatAnthropic\n", "from langgraph.prebuilt import create_react_agent\n", "from langgraph.graph import MessagesState, END\n", "from langgraph.types import Command\n", "\n", "\n", "llm = ChatAnthropic(model=\"claude-3-5-sonnet-latest\")\n", "\n", "\n", "def get_next_node(last_message: BaseMessage, goto: str):\n", "    if \"FINAL ANSWER\" in last_message.content:\n", "        # Any agent decided the work is done\n", "        return END\n", "    return goto\n", "\n", "\n", "# Research agent and node\n", "research_agent = create_react_agent(\n", "    llm,\n", "    tools=[tavily_tool],\n", "    prompt=make_system_prompt(\n", "        \"You can only do research. You are working with a chart generator colleague.\"\n", "    ),\n", ")\n", "\n", "\n", "def research_node(\n", "    state: MessagesState,\n", ") -> Command[Literal[\"chart_generator\", END]]:\n", "    result = research_agent.invoke(state)\n", "    goto = get_next_node(result[\"messages\"][-1], \"chart_generator\")\n", "    # wrap in a human message, as not all providers allow\n", "    # AI message at the last position of the input messages list\n", "    result[\"messages\"][-1] = HumanMessage(\n", "        content=result[\"messages\"][-1].content, name=\"researcher\"\n", "    )\n", "    return Command(\n", "        update={\n", "            # share internal message history of research agent with other agents\n", "            \"messages\": result[\"messages\"],\n", "        },\n", "        goto=goto,\n", "    )\n", "\n", "\n", "# Chart generator agent and node\n", "# NOTE: THIS PERFORMS ARBITRARY CODE EXECUTION, WHICH CAN BE UNSAFE WHEN NOT SANDBOXED\n", "chart_agent = create_react_agent(\n", "    llm,\n", "    [python_repl_tool],\n", "    prompt=make_system_prompt(\n", "        \"You can only generate charts. You are working with a researcher colleague.\"\n", "    ),\n", ")\n", "\n", "\n", "def chart_node(state: MessagesState) -> Command[Literal[\"researcher\", END]]:\n", "    result = chart_agent.invoke(state)\n", "    goto = get_next_node(result[\"messages\"][-1], \"researcher\")\n", "    # wrap in a human message, as not all providers allow\n", "    # AI message at the last position of the input messages list\n", "    result[\"messages\"][-1] = HumanMessage(\n", "        content=result[\"messages\"][-1].content, name=\"chart_generator\"\n", "    )\n", "    return Command(\n", "        update={\n", "            # share internal message history of chart agent with other agents\n", "            \"messages\": result[\"messages\"],\n", "        },\n", "        goto=goto,\n", "    )"]}, {"cell_type": "markdown", "id": "e9359c34-e191-43a2-a3d4-f2dea636dfd2", "metadata": {}, "source": ["### Define the Graph\n", "\n", "We can now put it all together and define the graph!"]}, {"cell_type": "code", "execution_count": 8, "id": "2c4a5ade-5912-494b-bf62-8a99278f9f12", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import StateGraph, START\n", "\n", "workflow = StateGraph(MessagesState)\n", "workflow.add_node(\"researcher\", research_node)\n", "workflow.add_node(\"chart_generator\", chart_node)\n", "\n", "workflow.add_edge(<PERSON><PERSON><PERSON>, \"researcher\")\n", "graph = workflow.compile()"]}, {"cell_type": "code", "execution_count": 9, "id": "97f8e0eb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "markdown", "id": "8c9447e7-9ab6-43eb-8ae6-9b52f8ba8425", "metadata": {}, "source": ["## Invoke\n", "\n", "With the graph created, you can invoke it! Let's have it chart some stats for us."]}, {"cell_type": "code", "execution_count": 10, "id": "9f478b05-3f09-447f-a9f4-1b2eae73f5ef", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'researcher': {'messages': [HumanMessage(content=\"First, get the UK's GDP over the past 5 years, then make a line chart of it. Once you make the chart, finish.\", additional_kwargs={}, response_metadata={}, id='fa1f5e95-9e1a-47d4-b4b6-e93f345e339d'), AIMessage(content=[{'text': \"I'll help search for the UK's GDP data over the past 5 years. Then my colleague can help create the line chart.\", 'type': 'text'}, {'id': 'toolu_01Jd9dxa4Ss2NhzBhCuwUX3E', 'input': {'query': 'UK GDP annual data past 5 years 2019-2023'}, 'name': 'tavily_search_results_json', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_014nCkfVHnG6LAsiS6pY7zcd', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 555, 'output_tokens': 101}}, id='run-e2297529-9972-4de6-835d-23d920b0e29b-0', tool_calls=[{'name': 'tavily_search_results_json', 'args': {'query': 'UK GDP annual data past 5 years 2019-2023'}, 'id': 'toolu_01Jd9dxa4Ss2NhzBhCuwUX3E', 'type': 'tool_call'}], usage_metadata={'input_tokens': 555, 'output_tokens': 101, 'total_tokens': 656, 'input_token_details': {}}), ToolMessage(content='[{\"url\": \"https://www.macrotrends.net/global-metrics/countries/GBR/united-kingdom/gdp-gross-domestic-product\", \"content\": \"Dollar figures for GDP are converted from domestic currencies using single year official exchange rates. For a few countries where the official exchange rate does not reflect the rate effectively applied to actual foreign exchange transactions, an alternative conversion factor is used. U.K. gdp for 2023 was $3,340.03B, a 8.13% increase from 2022.\"}, {\"url\": \"https://www.statista.com/topics/3795/gdp-of-the-uk/\", \"content\": \"Monthly growth of gross domestic product in the United Kingdom from January 2019 to November 2023\\\\nContribution to GDP growth in the UK 2023, by sector\\\\nContribution to gross domestic product growth in the United Kingdom in January 2023, by sector\\\\nGDP growth rate in the UK 1999-2021, by country\\\\nAnnual growth rates of gross domestic product in the United Kingdom from 1999 to 2021, by country\\\\nGDP growth rate in the UK 2021, by region\\\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\\\nGDP growth of Scotland 2021, by local area\\\\nAnnual growth rates of gross domestic product in Scotland in 2021, by local (ITL 3) area\\\\nGDP growth of Wales 2021, by local area\\\\nAnnual growth rates of gross domestic product in Wales in 2021, by local (ITL 3) area\\\\nGDP growth of Northern Ireland 2021, by local area\\\\nAnnual growth rates of gross domestic product in Northern Ireland in 2021, by local (ITL 3) area\\\\nGDP per capita\\\\nGDP per capita\\\\nGDP per capita in the UK 1955-2022\\\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\\\nAnnual GDP per capita growth in the UK 1956-2022\\\\nAnnual GDP per capita growth in the United Kingdom from 1956 to 2022\\\\nQuarterly GDP per capita in the UK 2019-2023\\\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\\\nQuarterly GDP per capita growth in the UK 2019-2023\\\\nQuarterly GDP per capita growth in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\\\nGDP per capita of the UK 1999-2021, by country\\\\nGross domestic product per capita of the United Kingdom from 1999 to 2021, by country (in GBP)\\\\nGDP per capita of the UK 2021, by region\\\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\\\nGlobal Comparisons\\\\nGlobal Comparisons\\\\nCountries with the largest gross domestic product (GDP) 2022\\\\n Monthly GDP of the UK 2019-2023\\\\nMonthly index of gross domestic product in the United Kingdom from January 2019 to November 2023 (2019=100)\\\\nGVA of the UK 2022, by sector\\\\nGross value added of the United Kingdom in 2022, by industry sector (in million GBP)\\\\nGDP of the UK 2021, by country\\\\nGross domestic product of the United Kingdom in 2021, by country (in million GBP)\\\\nGDP of the UK 2021, by region\\\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\\\nGDP of Scotland 2021, by local area\\\\nGross domestic product of Scotland in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP of Wales 2021, by local area\\\\nGross domestic product of Wales in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP of Northern Ireland 2021, by local area\\\\nGross domestic product of Northern Ireland in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP growth\\\\nGDP growth\\\\nGDP growth forecast for the UK 2000-2028\\\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\\\nAnnual GDP growth in the UK 1949-2022\\\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\\\nQuarterly GDP growth of the UK 2019-2023\\\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023\\\\nMonthly GDP growth of the UK 2019-2023\\\\n Transforming data into design:\\\\nStatista Content & Design\\\\nStrategy and business building for the data-driven economy:\\\\nUK GDP - Statistics & Facts\\\\nUK economy expected to shrink in 2023\\\\nCharacteristics of UK GDP\\\\nKey insights\\\\nDetailed statistics\\\\nGDP of the UK 1948-2022\\\\nDetailed statistics\\\\nAnnual GDP growth in the UK 1949-2022\\\\nDetailed statistics\\\\nGDP per capita in the UK 1955-2022\\\\nEditor’s Picks\\\\nCurrent statistics on this topic\\\\nCurrent statistics on this topic\\\\nKey Economic Indicators\\\\nMonthly GDP growth of the UK 2019-2023\\\\nKey Economic Indicators\\\\nMonthly GDP of the UK 2019-2023\\\\nKey Economic Indicators\\\\nContribution to GDP growth in the UK 2023, by sector\\\\nRelated topics\\\\nRecommended\\\\nRecommended statistics\\\\nGDP\\\\nGDP\\\\nGDP of the UK 1948-2022\\\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\\\nQuarterly GDP of the UK 2019-2023\\\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in million GBP)\\\\n The 20 countries with the largest gross domestic product (GDP) in 2022 (in billion U.S. dollars)\\\\nGDP of European countries in 2022\\\\nGross domestic product at current market prices of selected European countries in 2022 (in million euros)\\\\nReal GDP growth rates in Europe 2023\\\\nAnnual real gross domestic product (GDP) growth rate in European countries in 2023\\\\nGross domestic product (GDP) of Europe\\'s largest economies 1980-2028\\\\nGross domestic product (GDP) at current prices of Europe\\'s largest economies from 1980 to 2028 (in billion U.S dollars)\\\\nUnited Kingdom\\'s share of global gross domestic product (GDP) 2028\\\\nUnited Kingdom (UK): Share of global gross domestic product (GDP) adjusted for Purchasing Power Parity (PPP) from 2018 to 2028\\\\nRelated topics\\\\nRecommended\\\\nReport on the topic\\\\nKey figures\\\\nThe most important key figures provide you with a compact summary of the topic of \\\\\"UK GDP\\\\\" and take you straight to the corresponding statistics.\\\\n Industry Overview\\\\nDigital & Trend reports\\\\nOverview and forecasts on trending topics\\\\nIndustry & Market reports\\\\nIndustry and market insights and forecasts\\\\nCompanies & Products reports\\\\nKey figures and rankings about companies and products\\\\nConsumer & Brand reports\\\\nConsumer and brand insights and preferences in various industries\\\\nPolitics & Society reports\\\\nDetailed information about political and social topics\\\\nCountry & Region reports\\\\nAll key figures about countries and regions\\\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\\\nInsights on consumer attitudes and behavior worldwide\\\\nBusiness information on 100m+ public and private companies\\\\nExplore Company Insights\\\\nDetailed information for 39,000+ online stores and marketplaces\\\\nDirectly accessible data for 170 industries from 150+ countries\\\\nand over 1\\xa0Mio. facts.\\\\n\"}, {\"url\": \"https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB\", \"content\": \"GDP growth (annual %) - United Kingdom | Data - World Bank Data\"}, {\"url\": \"https://www.statista.com/topics/6500/the-british-economy/\", \"content\": \"Output per hour worked in the UK 1971 to 2023\\\\nEconomic output per hour worked in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023 (2019=100)\\\\nAnnual unemployment rate in the UK 2000-2028\\\\nAnnual unemployment rate in the United Kingdom from 2000 to 2028\\\\nInflation\\\\nInflation\\\\nInflation rate in the UK 1989-2023\\\\nInflation rate for the Consumer Price Index (CPI) in the United Kingdom from January 1989 to October 2023\\\\nRPI inflation rate in the UK 1948-2023\\\\nInflation rate for the Retail Price Index (RPI) in the United Kingdom from June 1948 to October 2023\\\\nCPIH inflation rate in the UK 1989-2023\\\\nInflation rate for the Consumer Price Index including owner occupiers\\' housing costs (CPIH) in the United Kingdom from January 1989 to October 2023\\\\nPPI in the UK 2010-2023\\\\nProducer Price Index (PPI) in the United Kingdom from October 2010 to October 2023\\\\nCPI inflation rate in the UK 2023, by sector\\\\nInflation rate for the Consumer Price Index (CPI) in the United Kingdom in October 2023, by sector\\\\nConsumer Price Index in the UK 1988-2023\\\\nConsumer Price Index (CPI) in the United Kingdom from 1st quarter 1988 to 3rd quarter 2023\\\\nRetail Price Index in the UK 1987-2023\\\\nRetail Price Index (RPI) in the United Kingdom from 1st quarter 1987 to 3rd quarter 2023\\\\nConsumer Price Index including housing in the UK 1988-2023\\\\nConsumer Price Index including owner occupiers\\' housing costs (CPIH) in the United Kingdom from 1st quarter 1988 to 3rd quarter 2023\\\\nRPI annual inflation rate UK 2000-2028\\\\nAnnual inflation rate of the Retail Price Index in the United Kingdom from 2000 to 2028\\\\nCPI annual inflation rate UK 2000-2028\\\\nAnnual inflation rate of the Consumer Price Index in the United Kingdom from 2000 to 2028\\\\nGovernment finances\\\\nGovernment finances\\\\nGovernment spending as a percentage of GDP in the UK 1900-2029\\\\nTotal managed expenditure expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\\\nGovernment revenue as a percentage of GDP in the UK 1900-2029\\\\nTotal public sector current receipts expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29 (in million GBP)\\\\nGovernment borrowing as a percentage of GDP in the UK 1900-2029\\\\nPublic sector borrowing expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\\\nNational debt as a percentage of GDP in the UK 1900-2029\\\\nPublic sector net debt expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\\\nPublic sector spending in the United Kingdom 2023/24\\\\nBudgeted public sector expenditure on services in the United Kingdom in 2023/24, by function (in billion GBP)\\\\nGovernment revenue sources in the United Kingdom 2023/24\\\\nExpected public sector current receipts in the United Kingdom in 2023/24, by function (in billion GBP)\\\\nBusiness Enterprise\\\\nBusiness Enterprise\\\\nLargest companies in the United Kingdom based on revenue 2022\\\\nLargest companies in the United Kingdom based on revenue in 2022 (in billion US dollars)\\\\nLargest UK companies based on number of global employees 2020\\\\nLargest companies based in the United Kingdom on number of employees worldwide in 2020 (in 1,000s)\\\\nNumber of private sector businesses in the UK 2000-2023\\\\nNumber of private sector businesses in the United Kingdom from 2000 to 2023 (in millions)\\\\nNumber of private sector businesses in the UK 2023, by sector\\\\nNumber of private sector businesses in the United Kingdom in 2023, by sector\\\\nNumber of businesses by enterprise size in the UK 2023\\\\nNumber of private sector businesses in the United Kingdom in 2023, by employment size\\\\nNumber of private sector businesses in the UK 2023, by region\\\\nNumber of private sector businesses in the United Kingdom in 2023, by region\\\\nNumber of local business units in the UK 2012-2023\\\\nNumber of local units in VAT and/or PAYE based enterprises in the United Kingdom from 2012 to 2023 (in millions)\\\\nBusiness investment index in the UK 1997-2023\\\\nBusiness investment index in the United Kingdom from 1st quarter 1997 to 2nd quarter 2023 (Q1 1997=100)\\\\nBusiness confidence Index in the UK 1977-2023\\\\nBusiness confidence Index of the United Kingdom from March 1977 to November 2023 (100 = long-term average)\\\\nRelated topics\\\\nRecommended\\\\nReport on the topic\\\\nKey figures\\\\nThe most important key figures provide you with a compact summary of the topic of \\\\\"The UK economy\\\\\" and take you straight to the corresponding statistics.\\\\n Monthly GDP growth of the UK 2020-2023\\\\nMonthly growth of gross domestic product in the United Kingdom from January 2020 to September 2023\\\\nGDP of the UK 2021, by region\\\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\\\nGDP growth rate in the UK 2021, by region\\\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\\\nGDP per capita of the UK 2021, by region\\\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\\\nGDP growth forecast for the UK 2000-2028\\\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\\\nLabor Market\\\\nLabor Market\\\\nUnemployment rate of the UK 1971-2023\\\\nUnemployment rate in the United Kingdom from March 1971 to September 2023\\\\nEmployment rate in the UK 1971-2022\\\\nEmployment rate in the United Kingdom from March 1971 to July 2023\\\\nNumber of people unemployed in the UK 1971-2023\\\\nNumber of people unemployed in the United Kingdom from March 1971 to July 2023 (in 1,000s)\\\\nNumber of people employed in the UK 1971-2021\\\\nNumber of people employed in the United Kingdom from March 1971 to July 2023 (in 1,000s)\\\\nUnemployment rate in the UK 1971-2023, by gender\\\\nUnemployment rate in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023, by gender\\\\nUnemployment rate in the UK 1992-2023, by age group\\\\nUnemployment rate in the United Kingdom from May 1992 to July 2023, by age group\\\\nYouth unemployment rate in the UK 1992-2023\\\\nYouth unemployment rate in the United Kingdom from May 1992 to July 2023\\\\nAverage annual earnings for full-time employees in the UK 1999-2023\\\\nMedian annual earnings for full-time employees in the United Kingdom from 1999 to 2023 (in nominal GBP)\\\\nAverage weekly earning growth in the UK 2001-2023\\\\nAverage year-on-year growth of weekly earnings (3 month average) in the United Kingdom from March 2001 to October 2023\\\\nNumber of redundancies in the UK 1995-2023\\\\nAverage number of people made redundant in the United Kingdom from May 1995 to July 2023 (in 1,000s)\\\\nOverall weekly hours worked in the UK 1971-2023\\\\nOverall weekly hours worked for all employees in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023 (in million hours worked)\\\\n Transforming data into design:\\\\nStatista Content & Design\\\\nStrategy and business building for the data-driven economy:\\\\nThe UK economy - Statistics & Facts\\\\nUK households under pressure in 2023\\\\nCoronavirus devastates UK economy in 2020\\\\nKey insights\\\\nDetailed statistics\\\\nGDP of the UK 1948-2022\\\\nDetailed statistics\\\\nUnemployment rate of the UK 1971-2023\\\\nDetailed statistics\\\\nInflation rate in the UK 1989-2023\\\\nEditor’s Picks\\\\nCurrent statistics on this topic\\\\nCurrent statistics on this topic\\\\nWages & Salaries\\\\nAverage weekly earning growth in the UK 2001-2023\\\\nIncome & Expenditure\\\\nPublic sector spending in the United Kingdom 2023/24\\\\nEmployment\\\\nNumber of people employed in the UK 1971-2021\\\\nRelated topics\\\\nRecommended\\\\nRecommended statistics\\\\nGross domestic product\\\\nGross domestic product\\\\nGDP of the UK 1948-2022\\\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\\\nAnnual GDP growth in the UK 1949-2022\\\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\\\nGDP per capita in the UK 1955-2022\\\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\\\nQuarterly GDP of the UK 1955-2023\\\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 1955 to 3rd quarter 2023 (in million GBP)\\\\nQuarterly GDP growth of the UK 2015-2023\\\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2015 to 3rd quarter 2023\\\\nQuarterly GDP per capita in the UK 1955-2023\\\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 1955 to 3rd quarter 2023 (in GBP)\\\\nMonthly GDP of the UK 1997-2023\\\\nMonthly index of gross domestic product in the United Kingdom from January 1997 to September 2023 (2019=100)\\\\n GDP\\\\nAnnual GDP growth in the UK 1949-2022\\\\nQuarterly GDP per capita growth in the UK 2015-2023\\\\nMonthly GDP growth of the UK 2020-2023\\\\nGDP per capita in the UK 1955-2022\\\\nLabor market\\\\nNumber of people employed in the UK 1971-2021\\\\nNumber of people unemployed in the UK 1971-2023\\\\nDaily number of jobs furloughed in the UK 2020-2021\\\\nAverage annual earnings for full-time employees in the UK 1999-2023\\\\nForecasts for 2023\\\\nGDP growth forecast for the UK 2000-2028\\\\nAnnual unemployment rate in the UK 2000-2028\\\\nCPI annual inflation rate UK 2000-2028\\\\nRPI annual inflation rate UK 2000-2028\\\\n Industry Overview\\\\nDigital & Trend reports\\\\nOverview and forecasts on trending topics\\\\nIndustry & Market reports\\\\nIndustry and market insights and forecasts\\\\nCompanies & Products reports\\\\nKey figures and rankings about companies and products\\\\nConsumer & Brand reports\\\\nConsumer and brand insights and preferences in various industries\\\\nPolitics & Society reports\\\\nDetailed information about political and social topics\\\\nCountry & Region reports\\\\nAll key figures about countries and regions\\\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\\\nInsights on consumer attitudes and behavior worldwide\\\\nBusiness information on 100m+ public and private companies\\\\nExplore Company Insights\\\\nDetailed information for 39,000+ online stores and marketplaces\\\\nDirectly accessible data for 170 industries from 150+ countries\\\\nand over 1\\xa0Mio. facts.\\\\n\"}, {\"url\": \"https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB&most_recent_value_desc=false\", \"content\": \"GDP growth (annual %) - United Kingdom | Data Data GDP growth (annual %)United Kingdom Data Catalog Data Programs International Debt Statistics Other Books and Reports For Developers GDP growth (annual %) - United Kingdom ====================================== Similar values Highest values Lowest values GDP (constant 2015 US$)  GDP (current US$)  GDP (constant LCU)  GDP: linked series (current LCU)  GDP, PPP (constant 2021 international $)  GDP (current LCU)  GDP, PPP (current international $)  GDP per capita growth (annual %)  Country Most Recent Value All Countries and Economies Country Most Recent Value This site uses cookies to optimize functionality and give you the best possible experience. If you continue to navigate this website beyond this page, cookies will be placed on your browser.\"}]', name='tavily_search_results_json', id='4c88089f-0ac4-4eeb-9141-722f0463b78d', tool_call_id='toolu_01Jd9dxa4Ss2NhzBhCuwUX3E', artifact={'query': 'UK GDP annual data past 5 years 2019-2023', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': 'U.K. GDP 1960-2024 - Macrotrends', 'url': 'https://www.macrotrends.net/global-metrics/countries/GBR/united-kingdom/gdp-gross-domestic-product', 'content': 'Dollar figures for GDP are converted from domestic currencies using single year official exchange rates. For a few countries where the official exchange rate does not reflect the rate effectively applied to actual foreign exchange transactions, an alternative conversion factor is used. U.K. gdp for 2023 was $3,340.03B, a 8.13% increase from 2022.', 'score': 0.97675806, 'raw_content': None}, {'title': 'UK GDP - Statistics & Facts | Statista', 'url': 'https://www.statista.com/topics/3795/gdp-of-the-uk/', 'content': 'Monthly growth of gross domestic product in the United Kingdom from January 2019 to November 2023\\nContribution to GDP growth in the UK 2023, by sector\\nContribution to gross domestic product growth in the United Kingdom in January 2023, by sector\\nGDP growth rate in the UK 1999-2021, by country\\nAnnual growth rates of gross domestic product in the United Kingdom from 1999 to 2021, by country\\nGDP growth rate in the UK 2021, by region\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\nGDP growth of Scotland 2021, by local area\\nAnnual growth rates of gross domestic product in Scotland in 2021, by local (ITL 3) area\\nGDP growth of Wales 2021, by local area\\nAnnual growth rates of gross domestic product in Wales in 2021, by local (ITL 3) area\\nGDP growth of Northern Ireland 2021, by local area\\nAnnual growth rates of gross domestic product in Northern Ireland in 2021, by local (ITL 3) area\\nGDP per capita\\nGDP per capita\\nGDP per capita in the UK 1955-2022\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\nAnnual GDP per capita growth in the UK 1956-2022\\nAnnual GDP per capita growth in the United Kingdom from 1956 to 2022\\nQuarterly GDP per capita in the UK 2019-2023\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\nQuarterly GDP per capita growth in the UK 2019-2023\\nQuarterly GDP per capita growth in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\nGDP per capita of the UK 1999-2021, by country\\nGross domestic product per capita of the United Kingdom from 1999 to 2021, by country (in GBP)\\nGDP per capita of the UK 2021, by region\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\nGlobal Comparisons\\nGlobal Comparisons\\nCountries with the largest gross domestic product (GDP) 2022\\n Monthly GDP of the UK 2019-2023\\nMonthly index of gross domestic product in the United Kingdom from January 2019 to November 2023 (2019=100)\\nGVA of the UK 2022, by sector\\nGross value added of the United Kingdom in 2022, by industry sector (in million GBP)\\nGDP of the UK 2021, by country\\nGross domestic product of the United Kingdom in 2021, by country (in million GBP)\\nGDP of the UK 2021, by region\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\nGDP of Scotland 2021, by local area\\nGross domestic product of Scotland in 2021, by local (ITL 3) area (in million GBP)\\nGDP of Wales 2021, by local area\\nGross domestic product of Wales in 2021, by local (ITL 3) area (in million GBP)\\nGDP of Northern Ireland 2021, by local area\\nGross domestic product of Northern Ireland in 2021, by local (ITL 3) area (in million GBP)\\nGDP growth\\nGDP growth\\nGDP growth forecast for the UK 2000-2028\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\nAnnual GDP growth in the UK 1949-2022\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\nQuarterly GDP growth of the UK 2019-2023\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023\\nMonthly GDP growth of the UK 2019-2023\\n Transforming data into design:\\nStatista Content & Design\\nStrategy and business building for the data-driven economy:\\nUK GDP - Statistics & Facts\\nUK economy expected to shrink in 2023\\nCharacteristics of UK GDP\\nKey insights\\nDetailed statistics\\nGDP of the UK 1948-2022\\nDetailed statistics\\nAnnual GDP growth in the UK 1949-2022\\nDetailed statistics\\nGDP per capita in the UK 1955-2022\\nEditor’s Picks\\nCurrent statistics on this topic\\nCurrent statistics on this topic\\nKey Economic Indicators\\nMonthly GDP growth of the UK 2019-2023\\nKey Economic Indicators\\nMonthly GDP of the UK 2019-2023\\nKey Economic Indicators\\nContribution to GDP growth in the UK 2023, by sector\\nRelated topics\\nRecommended\\nRecommended statistics\\nGDP\\nGDP\\nGDP of the UK 1948-2022\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\nQuarterly GDP of the UK 2019-2023\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in million GBP)\\n The 20 countries with the largest gross domestic product (GDP) in 2022 (in billion U.S. dollars)\\nGDP of European countries in 2022\\nGross domestic product at current market prices of selected European countries in 2022 (in million euros)\\nReal GDP growth rates in Europe 2023\\nAnnual real gross domestic product (GDP) growth rate in European countries in 2023\\nGross domestic product (GDP) of Europe\\'s largest economies 1980-2028\\nGross domestic product (GDP) at current prices of Europe\\'s largest economies from 1980 to 2028 (in billion U.S dollars)\\nUnited Kingdom\\'s share of global gross domestic product (GDP) 2028\\nUnited Kingdom (UK): Share of global gross domestic product (GDP) adjusted for Purchasing Power Parity (PPP) from 2018 to 2028\\nRelated topics\\nRecommended\\nReport on the topic\\nKey figures\\nThe most important key figures provide you with a compact summary of the topic of \"UK GDP\" and take you straight to the corresponding statistics.\\n Industry Overview\\nDigital & Trend reports\\nOverview and forecasts on trending topics\\nIndustry & Market reports\\nIndustry and market insights and forecasts\\nCompanies & Products reports\\nKey figures and rankings about companies and products\\nConsumer & Brand reports\\nConsumer and brand insights and preferences in various industries\\nPolitics & Society reports\\nDetailed information about political and social topics\\nCountry & Region reports\\nAll key figures about countries and regions\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\nInsights on consumer attitudes and behavior worldwide\\nBusiness information on 100m+ public and private companies\\nExplore Company Insights\\nDetailed information for 39,000+ online stores and marketplaces\\nDirectly accessible data for 170 industries from 150+ countries\\nand over 1\\xa0Mio. facts.\\n', 'score': 0.********, 'raw_content': None}, {'title': 'GDP growth (annual %) - United Kingdom | Data - World Bank Data', 'url': 'https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB', 'content': 'GDP growth (annual %) - United Kingdom | Data - World Bank Data', 'score': 0.********, 'raw_content': None}, {'title': 'The UK economy - Statistics & Facts | Statista', 'url': 'https://www.statista.com/topics/6500/the-british-economy/', 'content': 'Output per hour worked in the UK 1971 to 2023\\nEconomic output per hour worked in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023 (2019=100)\\nAnnual unemployment rate in the UK 2000-2028\\nAnnual unemployment rate in the United Kingdom from 2000 to 2028\\nInflation\\nInflation\\nInflation rate in the UK 1989-2023\\nInflation rate for the Consumer Price Index (CPI) in the United Kingdom from January 1989 to October 2023\\nRPI inflation rate in the UK 1948-2023\\nInflation rate for the Retail Price Index (RPI) in the United Kingdom from June 1948 to October 2023\\nCPIH inflation rate in the UK 1989-2023\\nInflation rate for the Consumer Price Index including owner occupiers\\' housing costs (CPIH) in the United Kingdom from January 1989 to October 2023\\nPPI in the UK 2010-2023\\nProducer Price Index (PPI) in the United Kingdom from October 2010 to October 2023\\nCPI inflation rate in the UK 2023, by sector\\nInflation rate for the Consumer Price Index (CPI) in the United Kingdom in October 2023, by sector\\nConsumer Price Index in the UK 1988-2023\\nConsumer Price Index (CPI) in the United Kingdom from 1st quarter 1988 to 3rd quarter 2023\\nRetail Price Index in the UK 1987-2023\\nRetail Price Index (RPI) in the United Kingdom from 1st quarter 1987 to 3rd quarter 2023\\nConsumer Price Index including housing in the UK 1988-2023\\nConsumer Price Index including owner occupiers\\' housing costs (CPIH) in the United Kingdom from 1st quarter 1988 to 3rd quarter 2023\\nRPI annual inflation rate UK 2000-2028\\nAnnual inflation rate of the Retail Price Index in the United Kingdom from 2000 to 2028\\nCPI annual inflation rate UK 2000-2028\\nAnnual inflation rate of the Consumer Price Index in the United Kingdom from 2000 to 2028\\nGovernment finances\\nGovernment finances\\nGovernment spending as a percentage of GDP in the UK 1900-2029\\nTotal managed expenditure expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\nGovernment revenue as a percentage of GDP in the UK 1900-2029\\nTotal public sector current receipts expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29 (in million GBP)\\nGovernment borrowing as a percentage of GDP in the UK 1900-2029\\nPublic sector borrowing expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\nNational debt as a percentage of GDP in the UK 1900-2029\\nPublic sector net debt expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\nPublic sector spending in the United Kingdom 2023/24\\nBudgeted public sector expenditure on services in the United Kingdom in 2023/24, by function (in billion GBP)\\nGovernment revenue sources in the United Kingdom 2023/24\\nExpected public sector current receipts in the United Kingdom in 2023/24, by function (in billion GBP)\\nBusiness Enterprise\\nBusiness Enterprise\\nLargest companies in the United Kingdom based on revenue 2022\\nLargest companies in the United Kingdom based on revenue in 2022 (in billion US dollars)\\nLargest UK companies based on number of global employees 2020\\nLargest companies based in the United Kingdom on number of employees worldwide in 2020 (in 1,000s)\\nNumber of private sector businesses in the UK 2000-2023\\nNumber of private sector businesses in the United Kingdom from 2000 to 2023 (in millions)\\nNumber of private sector businesses in the UK 2023, by sector\\nNumber of private sector businesses in the United Kingdom in 2023, by sector\\nNumber of businesses by enterprise size in the UK 2023\\nNumber of private sector businesses in the United Kingdom in 2023, by employment size\\nNumber of private sector businesses in the UK 2023, by region\\nNumber of private sector businesses in the United Kingdom in 2023, by region\\nNumber of local business units in the UK 2012-2023\\nNumber of local units in VAT and/or PAYE based enterprises in the United Kingdom from 2012 to 2023 (in millions)\\nBusiness investment index in the UK 1997-2023\\nBusiness investment index in the United Kingdom from 1st quarter 1997 to 2nd quarter 2023 (Q1 1997=100)\\nBusiness confidence Index in the UK 1977-2023\\nBusiness confidence Index of the United Kingdom from March 1977 to November 2023 (100 = long-term average)\\nRelated topics\\nRecommended\\nReport on the topic\\nKey figures\\nThe most important key figures provide you with a compact summary of the topic of \"The UK economy\" and take you straight to the corresponding statistics.\\n Monthly GDP growth of the UK 2020-2023\\nMonthly growth of gross domestic product in the United Kingdom from January 2020 to September 2023\\nGDP of the UK 2021, by region\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\nGDP growth rate in the UK 2021, by region\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\nGDP per capita of the UK 2021, by region\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\nGDP growth forecast for the UK 2000-2028\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\nLabor Market\\nLabor Market\\nUnemployment rate of the UK 1971-2023\\nUnemployment rate in the United Kingdom from March 1971 to September 2023\\nEmployment rate in the UK 1971-2022\\nEmployment rate in the United Kingdom from March 1971 to July 2023\\nNumber of people unemployed in the UK 1971-2023\\nNumber of people unemployed in the United Kingdom from March 1971 to July 2023 (in 1,000s)\\nNumber of people employed in the UK 1971-2021\\nNumber of people employed in the United Kingdom from March 1971 to July 2023 (in 1,000s)\\nUnemployment rate in the UK 1971-2023, by gender\\nUnemployment rate in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023, by gender\\nUnemployment rate in the UK 1992-2023, by age group\\nUnemployment rate in the United Kingdom from May 1992 to July 2023, by age group\\nYouth unemployment rate in the UK 1992-2023\\nYouth unemployment rate in the United Kingdom from May 1992 to July 2023\\nAverage annual earnings for full-time employees in the UK 1999-2023\\nMedian annual earnings for full-time employees in the United Kingdom from 1999 to 2023 (in nominal GBP)\\nAverage weekly earning growth in the UK 2001-2023\\nAverage year-on-year growth of weekly earnings (3 month average) in the United Kingdom from March 2001 to October 2023\\nNumber of redundancies in the UK 1995-2023\\nAverage number of people made redundant in the United Kingdom from May 1995 to July 2023 (in 1,000s)\\nOverall weekly hours worked in the UK 1971-2023\\nOverall weekly hours worked for all employees in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023 (in million hours worked)\\n Transforming data into design:\\nStatista Content & Design\\nStrategy and business building for the data-driven economy:\\nThe UK economy - Statistics & Facts\\nUK households under pressure in 2023\\nCoronavirus devastates UK economy in 2020\\nKey insights\\nDetailed statistics\\nGDP of the UK 1948-2022\\nDetailed statistics\\nUnemployment rate of the UK 1971-2023\\nDetailed statistics\\nInflation rate in the UK 1989-2023\\nEditor’s Picks\\nCurrent statistics on this topic\\nCurrent statistics on this topic\\nWages & Salaries\\nAverage weekly earning growth in the UK 2001-2023\\nIncome & Expenditure\\nPublic sector spending in the United Kingdom 2023/24\\nEmployment\\nNumber of people employed in the UK 1971-2021\\nRelated topics\\nRecommended\\nRecommended statistics\\nGross domestic product\\nGross domestic product\\nGDP of the UK 1948-2022\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\nAnnual GDP growth in the UK 1949-2022\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\nGDP per capita in the UK 1955-2022\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\nQuarterly GDP of the UK 1955-2023\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 1955 to 3rd quarter 2023 (in million GBP)\\nQuarterly GDP growth of the UK 2015-2023\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2015 to 3rd quarter 2023\\nQuarterly GDP per capita in the UK 1955-2023\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 1955 to 3rd quarter 2023 (in GBP)\\nMonthly GDP of the UK 1997-2023\\nMonthly index of gross domestic product in the United Kingdom from January 1997 to September 2023 (2019=100)\\n GDP\\nAnnual GDP growth in the UK 1949-2022\\nQuarterly GDP per capita growth in the UK 2015-2023\\nMonthly GDP growth of the UK 2020-2023\\nGDP per capita in the UK 1955-2022\\nLabor market\\nNumber of people employed in the UK 1971-2021\\nNumber of people unemployed in the UK 1971-2023\\nDaily number of jobs furloughed in the UK 2020-2021\\nAverage annual earnings for full-time employees in the UK 1999-2023\\nForecasts for 2023\\nGDP growth forecast for the UK 2000-2028\\nAnnual unemployment rate in the UK 2000-2028\\nCPI annual inflation rate UK 2000-2028\\nRPI annual inflation rate UK 2000-2028\\n Industry Overview\\nDigital & Trend reports\\nOverview and forecasts on trending topics\\nIndustry & Market reports\\nIndustry and market insights and forecasts\\nCompanies & Products reports\\nKey figures and rankings about companies and products\\nConsumer & Brand reports\\nConsumer and brand insights and preferences in various industries\\nPolitics & Society reports\\nDetailed information about political and social topics\\nCountry & Region reports\\nAll key figures about countries and regions\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\nInsights on consumer attitudes and behavior worldwide\\nBusiness information on 100m+ public and private companies\\nExplore Company Insights\\nDetailed information for 39,000+ online stores and marketplaces\\nDirectly accessible data for 170 industries from 150+ countries\\nand over 1\\xa0Mio. facts.\\n', 'score': 0.********, 'raw_content': None}, {'title': 'GDP growth (annual %) - United Kingdom | Data - World Bank Data', 'url': 'https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB&most_recent_value_desc=false', 'content': 'GDP growth (annual %) - United Kingdom | Data Data GDP growth (annual %)United Kingdom Data Catalog Data Programs International Debt Statistics Other Books and Reports For Developers GDP growth (annual %) - United Kingdom ====================================== Similar values Highest values Lowest values GDP (constant 2015 US$)  GDP (current US$)  GDP (constant LCU)  GDP: linked series (current LCU)  GDP, PPP (constant 2021 international $)  GDP (current LCU)  GDP, PPP (current international $)  GDP per capita growth (annual %)  Country Most Recent Value All Countries and Economies Country Most Recent Value This site uses cookies to optimize functionality and give you the best possible experience. If you continue to navigate this website beyond this page, cookies will be placed on your browser.', 'score': 0.7892337, 'raw_content': None}], 'response_time': 2.3}), AIMessage(content=[{'text': 'Let me search for more specific data.', 'type': 'text'}, {'id': 'toolu_019dPRXojLJoVNYFLzzSWw4w', 'input': {'query': 'UK GDP values by year 2019 2020 2021 2022 2023'}, 'name': 'tavily_search_results_json', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_01Ac9vcTFneb5dvcEYXJyf1P', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 5890, 'output_tokens': 87}}, id='run-3504417f-c0b5-4908-82e2-89a18abb1b8e-0', tool_calls=[{'name': 'tavily_search_results_json', 'args': {'query': 'UK GDP values by year 2019 2020 2021 2022 2023'}, 'id': 'toolu_019dPRXojLJoVNYFLzzSWw4w', 'type': 'tool_call'}], usage_metadata={'input_tokens': 5890, 'output_tokens': 87, 'total_tokens': 5977, 'input_token_details': {}}), ToolMessage(content='[{\"url\": \"https://www.macrotrends.net/global-metrics/countries/GBR/united-kingdom/gdp-gross-domestic-product\", \"content\": \"U.K. gdp for 2023 was $3,340.03B, a 8.13% increase from 2022. U.K. gdp for 2022 was $3,088.84B, a 1.68% decline from 2021. U.K. gdp for 2021 was $3,141.51B, a 16.45% increase from 2020. U.K. gdp for 2020 was $2,697.81B, a 5.39% decline from 2019.\"}, {\"url\": \"https://countryeconomy.com/gdp/uk?year=2023\", \"content\": \"Gross Domestic Product of United Kingdom grew 0.3% in 2023 compared to last year. This rate is 45 -tenths of one percent less than the figure of 4.8% published in 2022. The GDP figure in 2023 was $3,380,855 million, leaving United Kingdom placed 6th in the ranking of GDP of the 196 countries that we publish.\"}, {\"url\": \"https://www.statista.com/topics/3795/gdp-of-the-uk/\", \"content\": \"Monthly growth of gross domestic product in the United Kingdom from January 2019 to November 2023\\\\nContribution to GDP growth in the UK 2023, by sector\\\\nContribution to gross domestic product growth in the United Kingdom in January 2023, by sector\\\\nGDP growth rate in the UK 1999-2021, by country\\\\nAnnual growth rates of gross domestic product in the United Kingdom from 1999 to 2021, by country\\\\nGDP growth rate in the UK 2021, by region\\\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\\\nGDP growth of Scotland 2021, by local area\\\\nAnnual growth rates of gross domestic product in Scotland in 2021, by local (ITL 3) area\\\\nGDP growth of Wales 2021, by local area\\\\nAnnual growth rates of gross domestic product in Wales in 2021, by local (ITL 3) area\\\\nGDP growth of Northern Ireland 2021, by local area\\\\nAnnual growth rates of gross domestic product in Northern Ireland in 2021, by local (ITL 3) area\\\\nGDP per capita\\\\nGDP per capita\\\\nGDP per capita in the UK 1955-2022\\\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\\\nAnnual GDP per capita growth in the UK 1956-2022\\\\nAnnual GDP per capita growth in the United Kingdom from 1956 to 2022\\\\nQuarterly GDP per capita in the UK 2019-2023\\\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\\\nQuarterly GDP per capita growth in the UK 2019-2023\\\\nQuarterly GDP per capita growth in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\\\nGDP per capita of the UK 1999-2021, by country\\\\nGross domestic product per capita of the United Kingdom from 1999 to 2021, by country (in GBP)\\\\nGDP per capita of the UK 2021, by region\\\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\\\nGlobal Comparisons\\\\nGlobal Comparisons\\\\nCountries with the largest gross domestic product (GDP) 2022\\\\n Monthly GDP of the UK 2019-2023\\\\nMonthly index of gross domestic product in the United Kingdom from January 2019 to November 2023 (2019=100)\\\\nGVA of the UK 2022, by sector\\\\nGross value added of the United Kingdom in 2022, by industry sector (in million GBP)\\\\nGDP of the UK 2021, by country\\\\nGross domestic product of the United Kingdom in 2021, by country (in million GBP)\\\\nGDP of the UK 2021, by region\\\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\\\nGDP of Scotland 2021, by local area\\\\nGross domestic product of Scotland in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP of Wales 2021, by local area\\\\nGross domestic product of Wales in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP of Northern Ireland 2021, by local area\\\\nGross domestic product of Northern Ireland in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP growth\\\\nGDP growth\\\\nGDP growth forecast for the UK 2000-2028\\\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\\\nAnnual GDP growth in the UK 1949-2022\\\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\\\nQuarterly GDP growth of the UK 2019-2023\\\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023\\\\nMonthly GDP growth of the UK 2019-2023\\\\n Transforming data into design:\\\\nStatista Content & Design\\\\nStrategy and business building for the data-driven economy:\\\\nUK GDP - Statistics & Facts\\\\nUK economy expected to shrink in 2023\\\\nCharacteristics of UK GDP\\\\nKey insights\\\\nDetailed statistics\\\\nGDP of the UK 1948-2022\\\\nDetailed statistics\\\\nAnnual GDP growth in the UK 1949-2022\\\\nDetailed statistics\\\\nGDP per capita in the UK 1955-2022\\\\nEditor’s Picks\\\\nCurrent statistics on this topic\\\\nCurrent statistics on this topic\\\\nKey Economic Indicators\\\\nMonthly GDP growth of the UK 2019-2023\\\\nKey Economic Indicators\\\\nMonthly GDP of the UK 2019-2023\\\\nKey Economic Indicators\\\\nContribution to GDP growth in the UK 2023, by sector\\\\nRelated topics\\\\nRecommended\\\\nRecommended statistics\\\\nGDP\\\\nGDP\\\\nGDP of the UK 1948-2022\\\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\\\nQuarterly GDP of the UK 2019-2023\\\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in million GBP)\\\\n The 20 countries with the largest gross domestic product (GDP) in 2022 (in billion U.S. dollars)\\\\nGDP of European countries in 2022\\\\nGross domestic product at current market prices of selected European countries in 2022 (in million euros)\\\\nReal GDP growth rates in Europe 2023\\\\nAnnual real gross domestic product (GDP) growth rate in European countries in 2023\\\\nGross domestic product (GDP) of Europe\\'s largest economies 1980-2028\\\\nGross domestic product (GDP) at current prices of Europe\\'s largest economies from 1980 to 2028 (in billion U.S dollars)\\\\nUnited Kingdom\\'s share of global gross domestic product (GDP) 2028\\\\nUnited Kingdom (UK): Share of global gross domestic product (GDP) adjusted for Purchasing Power Parity (PPP) from 2018 to 2028\\\\nRelated topics\\\\nRecommended\\\\nReport on the topic\\\\nKey figures\\\\nThe most important key figures provide you with a compact summary of the topic of \\\\\"UK GDP\\\\\" and take you straight to the corresponding statistics.\\\\n Industry Overview\\\\nDigital & Trend reports\\\\nOverview and forecasts on trending topics\\\\nIndustry & Market reports\\\\nIndustry and market insights and forecasts\\\\nCompanies & Products reports\\\\nKey figures and rankings about companies and products\\\\nConsumer & Brand reports\\\\nConsumer and brand insights and preferences in various industries\\\\nPolitics & Society reports\\\\nDetailed information about political and social topics\\\\nCountry & Region reports\\\\nAll key figures about countries and regions\\\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\\\nInsights on consumer attitudes and behavior worldwide\\\\nBusiness information on 100m+ public and private companies\\\\nExplore Company Insights\\\\nDetailed information for 39,000+ online stores and marketplaces\\\\nDirectly accessible data for 170 industries from 150+ countries\\\\nand over 1\\xa0Mio. facts.\\\\n\"}, {\"url\": \"https://www.ons.gov.uk/economy/grossdomesticproductgdp/compendium/unitedkingdomnationalaccountsthebluebook/2024/nationalaccountsataglance\", \"content\": \"Real gross domestic product (GDP) is estimated to have increased by 0.3% in 2023, following a recovery from the impacts of the coronavirus (COVID-19) pandemic over the two previous years (Figure 1). Data for the UK are the Office for National Statistics (ONS) measure of real gross domestic product (GDP). Figure 9: Real GDP per head fell in 2023 when compared with 2022 in six G10 economies, including the UK Data for the UK are the Office for National Statistics (ONS) measure of real gross domestic product (GDP) per head. Download this chart Figure 9: Real GDP per head fell in 2023 when compared with 2022 in six G10 economies, including the UK\"}, {\"url\": \"https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB&most_recent_value_desc=false\", \"content\": \"GDP growth (annual %) - United Kingdom | Data Data GDP growth (annual %)United Kingdom Data Catalog Data Programs International Debt Statistics Other Books and Reports For Developers GDP growth (annual %) - United Kingdom ====================================== Similar values Highest values Lowest values GDP (constant 2015 US$)  GDP (current US$)  GDP (constant LCU)  GDP: linked series (current LCU)  GDP, PPP (constant 2021 international $)  GDP (current LCU)  GDP, PPP (current international $)  GDP per capita growth (annual %)  Country Most Recent Value All Countries and Economies Country Most Recent Value This site uses cookies to optimize functionality and give you the best possible experience. If you continue to navigate this website beyond this page, cookies will be placed on your browser.\"}]', name='tavily_search_results_json', id='84c571ca-27c6-4023-93a2-f0c2e8b6abb0', tool_call_id='toolu_019dPRXojLJoVNYFLzzSWw4w', artifact={'query': 'UK GDP values by year 2019 2020 2021 2022 2023', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': 'U.K. GDP 1960-2024 - Macrotrends', 'url': 'https://www.macrotrends.net/global-metrics/countries/GBR/united-kingdom/gdp-gross-domestic-product', 'content': 'U.K. gdp for 2023 was $3,340.03B, a 8.13% increase from 2022. U.K. gdp for 2022 was $3,088.84B, a 1.68% decline from 2021. U.K. gdp for 2021 was $3,141.51B, a 16.45% increase from 2020. U.K. gdp for 2020 was $2,697.81B, a 5.39% decline from 2019.', 'score': 0.9974491, 'raw_content': None}, {'title': 'United Kingdom (UK) GDP - Gross Domestic Product 2023', 'url': 'https://countryeconomy.com/gdp/uk?year=2023', 'content': 'Gross Domestic Product of United Kingdom grew 0.3% in 2023 compared to last year. This rate is 45 -tenths of one percent less than the figure of 4.8% published in 2022. The GDP figure in 2023 was $3,380,855 million, leaving United Kingdom placed 6th in the ranking of GDP of the 196 countries that we publish.', 'score': 0.9964064, 'raw_content': None}, {'title': 'UK GDP - Statistics & Facts | Statista', 'url': 'https://www.statista.com/topics/3795/gdp-of-the-uk/', 'content': 'Monthly growth of gross domestic product in the United Kingdom from January 2019 to November 2023\\nContribution to GDP growth in the UK 2023, by sector\\nContribution to gross domestic product growth in the United Kingdom in January 2023, by sector\\nGDP growth rate in the UK 1999-2021, by country\\nAnnual growth rates of gross domestic product in the United Kingdom from 1999 to 2021, by country\\nGDP growth rate in the UK 2021, by region\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\nGDP growth of Scotland 2021, by local area\\nAnnual growth rates of gross domestic product in Scotland in 2021, by local (ITL 3) area\\nGDP growth of Wales 2021, by local area\\nAnnual growth rates of gross domestic product in Wales in 2021, by local (ITL 3) area\\nGDP growth of Northern Ireland 2021, by local area\\nAnnual growth rates of gross domestic product in Northern Ireland in 2021, by local (ITL 3) area\\nGDP per capita\\nGDP per capita\\nGDP per capita in the UK 1955-2022\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\nAnnual GDP per capita growth in the UK 1956-2022\\nAnnual GDP per capita growth in the United Kingdom from 1956 to 2022\\nQuarterly GDP per capita in the UK 2019-2023\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\nQuarterly GDP per capita growth in the UK 2019-2023\\nQuarterly GDP per capita growth in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\nGDP per capita of the UK 1999-2021, by country\\nGross domestic product per capita of the United Kingdom from 1999 to 2021, by country (in GBP)\\nGDP per capita of the UK 2021, by region\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\nGlobal Comparisons\\nGlobal Comparisons\\nCountries with the largest gross domestic product (GDP) 2022\\n Monthly GDP of the UK 2019-2023\\nMonthly index of gross domestic product in the United Kingdom from January 2019 to November 2023 (2019=100)\\nGVA of the UK 2022, by sector\\nGross value added of the United Kingdom in 2022, by industry sector (in million GBP)\\nGDP of the UK 2021, by country\\nGross domestic product of the United Kingdom in 2021, by country (in million GBP)\\nGDP of the UK 2021, by region\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\nGDP of Scotland 2021, by local area\\nGross domestic product of Scotland in 2021, by local (ITL 3) area (in million GBP)\\nGDP of Wales 2021, by local area\\nGross domestic product of Wales in 2021, by local (ITL 3) area (in million GBP)\\nGDP of Northern Ireland 2021, by local area\\nGross domestic product of Northern Ireland in 2021, by local (ITL 3) area (in million GBP)\\nGDP growth\\nGDP growth\\nGDP growth forecast for the UK 2000-2028\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\nAnnual GDP growth in the UK 1949-2022\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\nQuarterly GDP growth of the UK 2019-2023\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023\\nMonthly GDP growth of the UK 2019-2023\\n Transforming data into design:\\nStatista Content & Design\\nStrategy and business building for the data-driven economy:\\nUK GDP - Statistics & Facts\\nUK economy expected to shrink in 2023\\nCharacteristics of UK GDP\\nKey insights\\nDetailed statistics\\nGDP of the UK 1948-2022\\nDetailed statistics\\nAnnual GDP growth in the UK 1949-2022\\nDetailed statistics\\nGDP per capita in the UK 1955-2022\\nEditor’s Picks\\nCurrent statistics on this topic\\nCurrent statistics on this topic\\nKey Economic Indicators\\nMonthly GDP growth of the UK 2019-2023\\nKey Economic Indicators\\nMonthly GDP of the UK 2019-2023\\nKey Economic Indicators\\nContribution to GDP growth in the UK 2023, by sector\\nRelated topics\\nRecommended\\nRecommended statistics\\nGDP\\nGDP\\nGDP of the UK 1948-2022\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\nQuarterly GDP of the UK 2019-2023\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in million GBP)\\n The 20 countries with the largest gross domestic product (GDP) in 2022 (in billion U.S. dollars)\\nGDP of European countries in 2022\\nGross domestic product at current market prices of selected European countries in 2022 (in million euros)\\nReal GDP growth rates in Europe 2023\\nAnnual real gross domestic product (GDP) growth rate in European countries in 2023\\nGross domestic product (GDP) of Europe\\'s largest economies 1980-2028\\nGross domestic product (GDP) at current prices of Europe\\'s largest economies from 1980 to 2028 (in billion U.S dollars)\\nUnited Kingdom\\'s share of global gross domestic product (GDP) 2028\\nUnited Kingdom (UK): Share of global gross domestic product (GDP) adjusted for Purchasing Power Parity (PPP) from 2018 to 2028\\nRelated topics\\nRecommended\\nReport on the topic\\nKey figures\\nThe most important key figures provide you with a compact summary of the topic of \"UK GDP\" and take you straight to the corresponding statistics.\\n Industry Overview\\nDigital & Trend reports\\nOverview and forecasts on trending topics\\nIndustry & Market reports\\nIndustry and market insights and forecasts\\nCompanies & Products reports\\nKey figures and rankings about companies and products\\nConsumer & Brand reports\\nConsumer and brand insights and preferences in various industries\\nPolitics & Society reports\\nDetailed information about political and social topics\\nCountry & Region reports\\nAll key figures about countries and regions\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\nInsights on consumer attitudes and behavior worldwide\\nBusiness information on 100m+ public and private companies\\nExplore Company Insights\\nDetailed information for 39,000+ online stores and marketplaces\\nDirectly accessible data for 170 industries from 150+ countries\\nand over 1\\xa0Mio. facts.\\n', 'score': 0.********, 'raw_content': None}, {'title': 'National accounts at a glance - Office for National Statistics', 'url': 'https://www.ons.gov.uk/economy/grossdomesticproductgdp/compendium/unitedkingdomnationalaccountsthebluebook/2024/nationalaccountsataglance', 'content': 'Real gross domestic product (GDP) is estimated to have increased by 0.3% in 2023, following a recovery from the impacts of the coronavirus (COVID-19) pandemic over the two previous years (Figure 1). Data for the UK are the Office for National Statistics (ONS) measure of real gross domestic product (GDP). Figure 9: Real GDP per head fell in 2023 when compared with 2022 in six G10 economies, including the UK Data for the UK are the Office for National Statistics (ONS) measure of real gross domestic product (GDP) per head. Download this chart Figure 9: Real GDP per head fell in 2023 when compared with 2022 in six G10 economies, including the UK', 'score': 0.975249, 'raw_content': None}, {'title': 'GDP growth (annual %) - United Kingdom | Data - World Bank Data', 'url': 'https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB&most_recent_value_desc=false', 'content': 'GDP growth (annual %) - United Kingdom | Data Data GDP growth (annual %)United Kingdom Data Catalog Data Programs International Debt Statistics Other Books and Reports For Developers GDP growth (annual %) - United Kingdom ====================================== Similar values Highest values Lowest values GDP (constant 2015 US$)  GDP (current US$)  GDP (constant LCU)  GDP: linked series (current LCU)  GDP, PPP (constant 2021 international $)  GDP (current LCU)  GDP, PPP (current international $)  GDP per capita growth (annual %)  Country Most Recent Value All Countries and Economies Country Most Recent Value This site uses cookies to optimize functionality and give you the best possible experience. If you continue to navigate this website beyond this page, cookies will be placed on your browser.', 'score': 0.********, 'raw_content': None}], 'response_time': 2.37}), HumanMessage(content='Based on the search results, I can provide the UK\\'s GDP values for the past 5 years (in billions of US dollars):\\n\\n2019: $2,851.54\\n2020: $2,697.81\\n2021: $3,141.51\\n2022: $3,088.84\\n2023: $3,340.03\\n\\nI\\'ll pass this data to my chart generator colleague to create a line chart. They should create a line chart with:\\n- Years 2019-2023 on the x-axis\\n- GDP values in billions USD on the y-axis\\n- Title: \"UK GDP 2019-2023\"\\n- Clear data points showing the values\\n\\nOver to you, chart generator colleague!', additional_kwargs={}, response_metadata={}, name='researcher', id='7e790b7a-7b06-4b45-a595-8736b53db844')]}}\n", "----\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Python REPL can execute arbitrary code. Use with caution.\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'chart_generator': {'messages': [HumanMessage(content=\"First, get the UK's GDP over the past 5 years, then make a line chart of it. Once you make the chart, finish.\", additional_kwargs={}, response_metadata={}, id='fa1f5e95-9e1a-47d4-b4b6-e93f345e339d'), AIMessage(content=[{'text': \"I'll help search for the UK's GDP data over the past 5 years. Then my colleague can help create the line chart.\", 'type': 'text'}, {'id': 'toolu_01Jd9dxa4Ss2NhzBhCuwUX3E', 'input': {'query': 'UK GDP annual data past 5 years 2019-2023'}, 'name': 'tavily_search_results_json', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_014nCkfVHnG6LAsiS6pY7zcd', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 555, 'output_tokens': 101}}, id='run-e2297529-9972-4de6-835d-23d920b0e29b-0', tool_calls=[{'name': 'tavily_search_results_json', 'args': {'query': 'UK GDP annual data past 5 years 2019-2023'}, 'id': 'toolu_01Jd9dxa4Ss2NhzBhCuwUX3E', 'type': 'tool_call'}], usage_metadata={'input_tokens': 555, 'output_tokens': 101, 'total_tokens': 656, 'input_token_details': {}}), ToolMessage(content='[{\"url\": \"https://www.macrotrends.net/global-metrics/countries/GBR/united-kingdom/gdp-gross-domestic-product\", \"content\": \"Dollar figures for GDP are converted from domestic currencies using single year official exchange rates. For a few countries where the official exchange rate does not reflect the rate effectively applied to actual foreign exchange transactions, an alternative conversion factor is used. U.K. gdp for 2023 was $3,340.03B, a 8.13% increase from 2022.\"}, {\"url\": \"https://www.statista.com/topics/3795/gdp-of-the-uk/\", \"content\": \"Monthly growth of gross domestic product in the United Kingdom from January 2019 to November 2023\\\\nContribution to GDP growth in the UK 2023, by sector\\\\nContribution to gross domestic product growth in the United Kingdom in January 2023, by sector\\\\nGDP growth rate in the UK 1999-2021, by country\\\\nAnnual growth rates of gross domestic product in the United Kingdom from 1999 to 2021, by country\\\\nGDP growth rate in the UK 2021, by region\\\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\\\nGDP growth of Scotland 2021, by local area\\\\nAnnual growth rates of gross domestic product in Scotland in 2021, by local (ITL 3) area\\\\nGDP growth of Wales 2021, by local area\\\\nAnnual growth rates of gross domestic product in Wales in 2021, by local (ITL 3) area\\\\nGDP growth of Northern Ireland 2021, by local area\\\\nAnnual growth rates of gross domestic product in Northern Ireland in 2021, by local (ITL 3) area\\\\nGDP per capita\\\\nGDP per capita\\\\nGDP per capita in the UK 1955-2022\\\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\\\nAnnual GDP per capita growth in the UK 1956-2022\\\\nAnnual GDP per capita growth in the United Kingdom from 1956 to 2022\\\\nQuarterly GDP per capita in the UK 2019-2023\\\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\\\nQuarterly GDP per capita growth in the UK 2019-2023\\\\nQuarterly GDP per capita growth in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\\\nGDP per capita of the UK 1999-2021, by country\\\\nGross domestic product per capita of the United Kingdom from 1999 to 2021, by country (in GBP)\\\\nGDP per capita of the UK 2021, by region\\\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\\\nGlobal Comparisons\\\\nGlobal Comparisons\\\\nCountries with the largest gross domestic product (GDP) 2022\\\\n Monthly GDP of the UK 2019-2023\\\\nMonthly index of gross domestic product in the United Kingdom from January 2019 to November 2023 (2019=100)\\\\nGVA of the UK 2022, by sector\\\\nGross value added of the United Kingdom in 2022, by industry sector (in million GBP)\\\\nGDP of the UK 2021, by country\\\\nGross domestic product of the United Kingdom in 2021, by country (in million GBP)\\\\nGDP of the UK 2021, by region\\\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\\\nGDP of Scotland 2021, by local area\\\\nGross domestic product of Scotland in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP of Wales 2021, by local area\\\\nGross domestic product of Wales in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP of Northern Ireland 2021, by local area\\\\nGross domestic product of Northern Ireland in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP growth\\\\nGDP growth\\\\nGDP growth forecast for the UK 2000-2028\\\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\\\nAnnual GDP growth in the UK 1949-2022\\\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\\\nQuarterly GDP growth of the UK 2019-2023\\\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023\\\\nMonthly GDP growth of the UK 2019-2023\\\\n Transforming data into design:\\\\nStatista Content & Design\\\\nStrategy and business building for the data-driven economy:\\\\nUK GDP - Statistics & Facts\\\\nUK economy expected to shrink in 2023\\\\nCharacteristics of UK GDP\\\\nKey insights\\\\nDetailed statistics\\\\nGDP of the UK 1948-2022\\\\nDetailed statistics\\\\nAnnual GDP growth in the UK 1949-2022\\\\nDetailed statistics\\\\nGDP per capita in the UK 1955-2022\\\\nEditor’s Picks\\\\nCurrent statistics on this topic\\\\nCurrent statistics on this topic\\\\nKey Economic Indicators\\\\nMonthly GDP growth of the UK 2019-2023\\\\nKey Economic Indicators\\\\nMonthly GDP of the UK 2019-2023\\\\nKey Economic Indicators\\\\nContribution to GDP growth in the UK 2023, by sector\\\\nRelated topics\\\\nRecommended\\\\nRecommended statistics\\\\nGDP\\\\nGDP\\\\nGDP of the UK 1948-2022\\\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\\\nQuarterly GDP of the UK 2019-2023\\\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in million GBP)\\\\n The 20 countries with the largest gross domestic product (GDP) in 2022 (in billion U.S. dollars)\\\\nGDP of European countries in 2022\\\\nGross domestic product at current market prices of selected European countries in 2022 (in million euros)\\\\nReal GDP growth rates in Europe 2023\\\\nAnnual real gross domestic product (GDP) growth rate in European countries in 2023\\\\nGross domestic product (GDP) of Europe\\'s largest economies 1980-2028\\\\nGross domestic product (GDP) at current prices of Europe\\'s largest economies from 1980 to 2028 (in billion U.S dollars)\\\\nUnited Kingdom\\'s share of global gross domestic product (GDP) 2028\\\\nUnited Kingdom (UK): Share of global gross domestic product (GDP) adjusted for Purchasing Power Parity (PPP) from 2018 to 2028\\\\nRelated topics\\\\nRecommended\\\\nReport on the topic\\\\nKey figures\\\\nThe most important key figures provide you with a compact summary of the topic of \\\\\"UK GDP\\\\\" and take you straight to the corresponding statistics.\\\\n Industry Overview\\\\nDigital & Trend reports\\\\nOverview and forecasts on trending topics\\\\nIndustry & Market reports\\\\nIndustry and market insights and forecasts\\\\nCompanies & Products reports\\\\nKey figures and rankings about companies and products\\\\nConsumer & Brand reports\\\\nConsumer and brand insights and preferences in various industries\\\\nPolitics & Society reports\\\\nDetailed information about political and social topics\\\\nCountry & Region reports\\\\nAll key figures about countries and regions\\\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\\\nInsights on consumer attitudes and behavior worldwide\\\\nBusiness information on 100m+ public and private companies\\\\nExplore Company Insights\\\\nDetailed information for 39,000+ online stores and marketplaces\\\\nDirectly accessible data for 170 industries from 150+ countries\\\\nand over 1\\xa0Mio. facts.\\\\n\"}, {\"url\": \"https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB\", \"content\": \"GDP growth (annual %) - United Kingdom | Data - World Bank Data\"}, {\"url\": \"https://www.statista.com/topics/6500/the-british-economy/\", \"content\": \"Output per hour worked in the UK 1971 to 2023\\\\nEconomic output per hour worked in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023 (2019=100)\\\\nAnnual unemployment rate in the UK 2000-2028\\\\nAnnual unemployment rate in the United Kingdom from 2000 to 2028\\\\nInflation\\\\nInflation\\\\nInflation rate in the UK 1989-2023\\\\nInflation rate for the Consumer Price Index (CPI) in the United Kingdom from January 1989 to October 2023\\\\nRPI inflation rate in the UK 1948-2023\\\\nInflation rate for the Retail Price Index (RPI) in the United Kingdom from June 1948 to October 2023\\\\nCPIH inflation rate in the UK 1989-2023\\\\nInflation rate for the Consumer Price Index including owner occupiers\\' housing costs (CPIH) in the United Kingdom from January 1989 to October 2023\\\\nPPI in the UK 2010-2023\\\\nProducer Price Index (PPI) in the United Kingdom from October 2010 to October 2023\\\\nCPI inflation rate in the UK 2023, by sector\\\\nInflation rate for the Consumer Price Index (CPI) in the United Kingdom in October 2023, by sector\\\\nConsumer Price Index in the UK 1988-2023\\\\nConsumer Price Index (CPI) in the United Kingdom from 1st quarter 1988 to 3rd quarter 2023\\\\nRetail Price Index in the UK 1987-2023\\\\nRetail Price Index (RPI) in the United Kingdom from 1st quarter 1987 to 3rd quarter 2023\\\\nConsumer Price Index including housing in the UK 1988-2023\\\\nConsumer Price Index including owner occupiers\\' housing costs (CPIH) in the United Kingdom from 1st quarter 1988 to 3rd quarter 2023\\\\nRPI annual inflation rate UK 2000-2028\\\\nAnnual inflation rate of the Retail Price Index in the United Kingdom from 2000 to 2028\\\\nCPI annual inflation rate UK 2000-2028\\\\nAnnual inflation rate of the Consumer Price Index in the United Kingdom from 2000 to 2028\\\\nGovernment finances\\\\nGovernment finances\\\\nGovernment spending as a percentage of GDP in the UK 1900-2029\\\\nTotal managed expenditure expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\\\nGovernment revenue as a percentage of GDP in the UK 1900-2029\\\\nTotal public sector current receipts expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29 (in million GBP)\\\\nGovernment borrowing as a percentage of GDP in the UK 1900-2029\\\\nPublic sector borrowing expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\\\nNational debt as a percentage of GDP in the UK 1900-2029\\\\nPublic sector net debt expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\\\nPublic sector spending in the United Kingdom 2023/24\\\\nBudgeted public sector expenditure on services in the United Kingdom in 2023/24, by function (in billion GBP)\\\\nGovernment revenue sources in the United Kingdom 2023/24\\\\nExpected public sector current receipts in the United Kingdom in 2023/24, by function (in billion GBP)\\\\nBusiness Enterprise\\\\nBusiness Enterprise\\\\nLargest companies in the United Kingdom based on revenue 2022\\\\nLargest companies in the United Kingdom based on revenue in 2022 (in billion US dollars)\\\\nLargest UK companies based on number of global employees 2020\\\\nLargest companies based in the United Kingdom on number of employees worldwide in 2020 (in 1,000s)\\\\nNumber of private sector businesses in the UK 2000-2023\\\\nNumber of private sector businesses in the United Kingdom from 2000 to 2023 (in millions)\\\\nNumber of private sector businesses in the UK 2023, by sector\\\\nNumber of private sector businesses in the United Kingdom in 2023, by sector\\\\nNumber of businesses by enterprise size in the UK 2023\\\\nNumber of private sector businesses in the United Kingdom in 2023, by employment size\\\\nNumber of private sector businesses in the UK 2023, by region\\\\nNumber of private sector businesses in the United Kingdom in 2023, by region\\\\nNumber of local business units in the UK 2012-2023\\\\nNumber of local units in VAT and/or PAYE based enterprises in the United Kingdom from 2012 to 2023 (in millions)\\\\nBusiness investment index in the UK 1997-2023\\\\nBusiness investment index in the United Kingdom from 1st quarter 1997 to 2nd quarter 2023 (Q1 1997=100)\\\\nBusiness confidence Index in the UK 1977-2023\\\\nBusiness confidence Index of the United Kingdom from March 1977 to November 2023 (100 = long-term average)\\\\nRelated topics\\\\nRecommended\\\\nReport on the topic\\\\nKey figures\\\\nThe most important key figures provide you with a compact summary of the topic of \\\\\"The UK economy\\\\\" and take you straight to the corresponding statistics.\\\\n Monthly GDP growth of the UK 2020-2023\\\\nMonthly growth of gross domestic product in the United Kingdom from January 2020 to September 2023\\\\nGDP of the UK 2021, by region\\\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\\\nGDP growth rate in the UK 2021, by region\\\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\\\nGDP per capita of the UK 2021, by region\\\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\\\nGDP growth forecast for the UK 2000-2028\\\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\\\nLabor Market\\\\nLabor Market\\\\nUnemployment rate of the UK 1971-2023\\\\nUnemployment rate in the United Kingdom from March 1971 to September 2023\\\\nEmployment rate in the UK 1971-2022\\\\nEmployment rate in the United Kingdom from March 1971 to July 2023\\\\nNumber of people unemployed in the UK 1971-2023\\\\nNumber of people unemployed in the United Kingdom from March 1971 to July 2023 (in 1,000s)\\\\nNumber of people employed in the UK 1971-2021\\\\nNumber of people employed in the United Kingdom from March 1971 to July 2023 (in 1,000s)\\\\nUnemployment rate in the UK 1971-2023, by gender\\\\nUnemployment rate in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023, by gender\\\\nUnemployment rate in the UK 1992-2023, by age group\\\\nUnemployment rate in the United Kingdom from May 1992 to July 2023, by age group\\\\nYouth unemployment rate in the UK 1992-2023\\\\nYouth unemployment rate in the United Kingdom from May 1992 to July 2023\\\\nAverage annual earnings for full-time employees in the UK 1999-2023\\\\nMedian annual earnings for full-time employees in the United Kingdom from 1999 to 2023 (in nominal GBP)\\\\nAverage weekly earning growth in the UK 2001-2023\\\\nAverage year-on-year growth of weekly earnings (3 month average) in the United Kingdom from March 2001 to October 2023\\\\nNumber of redundancies in the UK 1995-2023\\\\nAverage number of people made redundant in the United Kingdom from May 1995 to July 2023 (in 1,000s)\\\\nOverall weekly hours worked in the UK 1971-2023\\\\nOverall weekly hours worked for all employees in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023 (in million hours worked)\\\\n Transforming data into design:\\\\nStatista Content & Design\\\\nStrategy and business building for the data-driven economy:\\\\nThe UK economy - Statistics & Facts\\\\nUK households under pressure in 2023\\\\nCoronavirus devastates UK economy in 2020\\\\nKey insights\\\\nDetailed statistics\\\\nGDP of the UK 1948-2022\\\\nDetailed statistics\\\\nUnemployment rate of the UK 1971-2023\\\\nDetailed statistics\\\\nInflation rate in the UK 1989-2023\\\\nEditor’s Picks\\\\nCurrent statistics on this topic\\\\nCurrent statistics on this topic\\\\nWages & Salaries\\\\nAverage weekly earning growth in the UK 2001-2023\\\\nIncome & Expenditure\\\\nPublic sector spending in the United Kingdom 2023/24\\\\nEmployment\\\\nNumber of people employed in the UK 1971-2021\\\\nRelated topics\\\\nRecommended\\\\nRecommended statistics\\\\nGross domestic product\\\\nGross domestic product\\\\nGDP of the UK 1948-2022\\\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\\\nAnnual GDP growth in the UK 1949-2022\\\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\\\nGDP per capita in the UK 1955-2022\\\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\\\nQuarterly GDP of the UK 1955-2023\\\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 1955 to 3rd quarter 2023 (in million GBP)\\\\nQuarterly GDP growth of the UK 2015-2023\\\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2015 to 3rd quarter 2023\\\\nQuarterly GDP per capita in the UK 1955-2023\\\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 1955 to 3rd quarter 2023 (in GBP)\\\\nMonthly GDP of the UK 1997-2023\\\\nMonthly index of gross domestic product in the United Kingdom from January 1997 to September 2023 (2019=100)\\\\n GDP\\\\nAnnual GDP growth in the UK 1949-2022\\\\nQuarterly GDP per capita growth in the UK 2015-2023\\\\nMonthly GDP growth of the UK 2020-2023\\\\nGDP per capita in the UK 1955-2022\\\\nLabor market\\\\nNumber of people employed in the UK 1971-2021\\\\nNumber of people unemployed in the UK 1971-2023\\\\nDaily number of jobs furloughed in the UK 2020-2021\\\\nAverage annual earnings for full-time employees in the UK 1999-2023\\\\nForecasts for 2023\\\\nGDP growth forecast for the UK 2000-2028\\\\nAnnual unemployment rate in the UK 2000-2028\\\\nCPI annual inflation rate UK 2000-2028\\\\nRPI annual inflation rate UK 2000-2028\\\\n Industry Overview\\\\nDigital & Trend reports\\\\nOverview and forecasts on trending topics\\\\nIndustry & Market reports\\\\nIndustry and market insights and forecasts\\\\nCompanies & Products reports\\\\nKey figures and rankings about companies and products\\\\nConsumer & Brand reports\\\\nConsumer and brand insights and preferences in various industries\\\\nPolitics & Society reports\\\\nDetailed information about political and social topics\\\\nCountry & Region reports\\\\nAll key figures about countries and regions\\\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\\\nInsights on consumer attitudes and behavior worldwide\\\\nBusiness information on 100m+ public and private companies\\\\nExplore Company Insights\\\\nDetailed information for 39,000+ online stores and marketplaces\\\\nDirectly accessible data for 170 industries from 150+ countries\\\\nand over 1\\xa0Mio. facts.\\\\n\"}, {\"url\": \"https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB&most_recent_value_desc=false\", \"content\": \"GDP growth (annual %) - United Kingdom | Data Data GDP growth (annual %)United Kingdom Data Catalog Data Programs International Debt Statistics Other Books and Reports For Developers GDP growth (annual %) - United Kingdom ====================================== Similar values Highest values Lowest values GDP (constant 2015 US$)  GDP (current US$)  GDP (constant LCU)  GDP: linked series (current LCU)  GDP, PPP (constant 2021 international $)  GDP (current LCU)  GDP, PPP (current international $)  GDP per capita growth (annual %)  Country Most Recent Value All Countries and Economies Country Most Recent Value This site uses cookies to optimize functionality and give you the best possible experience. If you continue to navigate this website beyond this page, cookies will be placed on your browser.\"}]', name='tavily_search_results_json', id='4c88089f-0ac4-4eeb-9141-722f0463b78d', tool_call_id='toolu_01Jd9dxa4Ss2NhzBhCuwUX3E', artifact={'query': 'UK GDP annual data past 5 years 2019-2023', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': 'U.K. GDP 1960-2024 - Macrotrends', 'url': 'https://www.macrotrends.net/global-metrics/countries/GBR/united-kingdom/gdp-gross-domestic-product', 'content': 'Dollar figures for GDP are converted from domestic currencies using single year official exchange rates. For a few countries where the official exchange rate does not reflect the rate effectively applied to actual foreign exchange transactions, an alternative conversion factor is used. U.K. gdp for 2023 was $3,340.03B, a 8.13% increase from 2022.', 'score': 0.97675806, 'raw_content': None}, {'title': 'UK GDP - Statistics & Facts | Statista', 'url': 'https://www.statista.com/topics/3795/gdp-of-the-uk/', 'content': 'Monthly growth of gross domestic product in the United Kingdom from January 2019 to November 2023\\nContribution to GDP growth in the UK 2023, by sector\\nContribution to gross domestic product growth in the United Kingdom in January 2023, by sector\\nGDP growth rate in the UK 1999-2021, by country\\nAnnual growth rates of gross domestic product in the United Kingdom from 1999 to 2021, by country\\nGDP growth rate in the UK 2021, by region\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\nGDP growth of Scotland 2021, by local area\\nAnnual growth rates of gross domestic product in Scotland in 2021, by local (ITL 3) area\\nGDP growth of Wales 2021, by local area\\nAnnual growth rates of gross domestic product in Wales in 2021, by local (ITL 3) area\\nGDP growth of Northern Ireland 2021, by local area\\nAnnual growth rates of gross domestic product in Northern Ireland in 2021, by local (ITL 3) area\\nGDP per capita\\nGDP per capita\\nGDP per capita in the UK 1955-2022\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\nAnnual GDP per capita growth in the UK 1956-2022\\nAnnual GDP per capita growth in the United Kingdom from 1956 to 2022\\nQuarterly GDP per capita in the UK 2019-2023\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\nQuarterly GDP per capita growth in the UK 2019-2023\\nQuarterly GDP per capita growth in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\nGDP per capita of the UK 1999-2021, by country\\nGross domestic product per capita of the United Kingdom from 1999 to 2021, by country (in GBP)\\nGDP per capita of the UK 2021, by region\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\nGlobal Comparisons\\nGlobal Comparisons\\nCountries with the largest gross domestic product (GDP) 2022\\n Monthly GDP of the UK 2019-2023\\nMonthly index of gross domestic product in the United Kingdom from January 2019 to November 2023 (2019=100)\\nGVA of the UK 2022, by sector\\nGross value added of the United Kingdom in 2022, by industry sector (in million GBP)\\nGDP of the UK 2021, by country\\nGross domestic product of the United Kingdom in 2021, by country (in million GBP)\\nGDP of the UK 2021, by region\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\nGDP of Scotland 2021, by local area\\nGross domestic product of Scotland in 2021, by local (ITL 3) area (in million GBP)\\nGDP of Wales 2021, by local area\\nGross domestic product of Wales in 2021, by local (ITL 3) area (in million GBP)\\nGDP of Northern Ireland 2021, by local area\\nGross domestic product of Northern Ireland in 2021, by local (ITL 3) area (in million GBP)\\nGDP growth\\nGDP growth\\nGDP growth forecast for the UK 2000-2028\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\nAnnual GDP growth in the UK 1949-2022\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\nQuarterly GDP growth of the UK 2019-2023\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023\\nMonthly GDP growth of the UK 2019-2023\\n Transforming data into design:\\nStatista Content & Design\\nStrategy and business building for the data-driven economy:\\nUK GDP - Statistics & Facts\\nUK economy expected to shrink in 2023\\nCharacteristics of UK GDP\\nKey insights\\nDetailed statistics\\nGDP of the UK 1948-2022\\nDetailed statistics\\nAnnual GDP growth in the UK 1949-2022\\nDetailed statistics\\nGDP per capita in the UK 1955-2022\\nEditor’s Picks\\nCurrent statistics on this topic\\nCurrent statistics on this topic\\nKey Economic Indicators\\nMonthly GDP growth of the UK 2019-2023\\nKey Economic Indicators\\nMonthly GDP of the UK 2019-2023\\nKey Economic Indicators\\nContribution to GDP growth in the UK 2023, by sector\\nRelated topics\\nRecommended\\nRecommended statistics\\nGDP\\nGDP\\nGDP of the UK 1948-2022\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\nQuarterly GDP of the UK 2019-2023\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in million GBP)\\n The 20 countries with the largest gross domestic product (GDP) in 2022 (in billion U.S. dollars)\\nGDP of European countries in 2022\\nGross domestic product at current market prices of selected European countries in 2022 (in million euros)\\nReal GDP growth rates in Europe 2023\\nAnnual real gross domestic product (GDP) growth rate in European countries in 2023\\nGross domestic product (GDP) of Europe\\'s largest economies 1980-2028\\nGross domestic product (GDP) at current prices of Europe\\'s largest economies from 1980 to 2028 (in billion U.S dollars)\\nUnited Kingdom\\'s share of global gross domestic product (GDP) 2028\\nUnited Kingdom (UK): Share of global gross domestic product (GDP) adjusted for Purchasing Power Parity (PPP) from 2018 to 2028\\nRelated topics\\nRecommended\\nReport on the topic\\nKey figures\\nThe most important key figures provide you with a compact summary of the topic of \"UK GDP\" and take you straight to the corresponding statistics.\\n Industry Overview\\nDigital & Trend reports\\nOverview and forecasts on trending topics\\nIndustry & Market reports\\nIndustry and market insights and forecasts\\nCompanies & Products reports\\nKey figures and rankings about companies and products\\nConsumer & Brand reports\\nConsumer and brand insights and preferences in various industries\\nPolitics & Society reports\\nDetailed information about political and social topics\\nCountry & Region reports\\nAll key figures about countries and regions\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\nInsights on consumer attitudes and behavior worldwide\\nBusiness information on 100m+ public and private companies\\nExplore Company Insights\\nDetailed information for 39,000+ online stores and marketplaces\\nDirectly accessible data for 170 industries from 150+ countries\\nand over 1\\xa0Mio. facts.\\n', 'score': 0.********, 'raw_content': None}, {'title': 'GDP growth (annual %) - United Kingdom | Data - World Bank Data', 'url': 'https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB', 'content': 'GDP growth (annual %) - United Kingdom | Data - World Bank Data', 'score': 0.********, 'raw_content': None}, {'title': 'The UK economy - Statistics & Facts | Statista', 'url': 'https://www.statista.com/topics/6500/the-british-economy/', 'content': 'Output per hour worked in the UK 1971 to 2023\\nEconomic output per hour worked in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023 (2019=100)\\nAnnual unemployment rate in the UK 2000-2028\\nAnnual unemployment rate in the United Kingdom from 2000 to 2028\\nInflation\\nInflation\\nInflation rate in the UK 1989-2023\\nInflation rate for the Consumer Price Index (CPI) in the United Kingdom from January 1989 to October 2023\\nRPI inflation rate in the UK 1948-2023\\nInflation rate for the Retail Price Index (RPI) in the United Kingdom from June 1948 to October 2023\\nCPIH inflation rate in the UK 1989-2023\\nInflation rate for the Consumer Price Index including owner occupiers\\' housing costs (CPIH) in the United Kingdom from January 1989 to October 2023\\nPPI in the UK 2010-2023\\nProducer Price Index (PPI) in the United Kingdom from October 2010 to October 2023\\nCPI inflation rate in the UK 2023, by sector\\nInflation rate for the Consumer Price Index (CPI) in the United Kingdom in October 2023, by sector\\nConsumer Price Index in the UK 1988-2023\\nConsumer Price Index (CPI) in the United Kingdom from 1st quarter 1988 to 3rd quarter 2023\\nRetail Price Index in the UK 1987-2023\\nRetail Price Index (RPI) in the United Kingdom from 1st quarter 1987 to 3rd quarter 2023\\nConsumer Price Index including housing in the UK 1988-2023\\nConsumer Price Index including owner occupiers\\' housing costs (CPIH) in the United Kingdom from 1st quarter 1988 to 3rd quarter 2023\\nRPI annual inflation rate UK 2000-2028\\nAnnual inflation rate of the Retail Price Index in the United Kingdom from 2000 to 2028\\nCPI annual inflation rate UK 2000-2028\\nAnnual inflation rate of the Consumer Price Index in the United Kingdom from 2000 to 2028\\nGovernment finances\\nGovernment finances\\nGovernment spending as a percentage of GDP in the UK 1900-2029\\nTotal managed expenditure expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\nGovernment revenue as a percentage of GDP in the UK 1900-2029\\nTotal public sector current receipts expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29 (in million GBP)\\nGovernment borrowing as a percentage of GDP in the UK 1900-2029\\nPublic sector borrowing expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\nNational debt as a percentage of GDP in the UK 1900-2029\\nPublic sector net debt expressed as a percentage of GDP in the United Kingdom from 1900/01 to 2028/29\\nPublic sector spending in the United Kingdom 2023/24\\nBudgeted public sector expenditure on services in the United Kingdom in 2023/24, by function (in billion GBP)\\nGovernment revenue sources in the United Kingdom 2023/24\\nExpected public sector current receipts in the United Kingdom in 2023/24, by function (in billion GBP)\\nBusiness Enterprise\\nBusiness Enterprise\\nLargest companies in the United Kingdom based on revenue 2022\\nLargest companies in the United Kingdom based on revenue in 2022 (in billion US dollars)\\nLargest UK companies based on number of global employees 2020\\nLargest companies based in the United Kingdom on number of employees worldwide in 2020 (in 1,000s)\\nNumber of private sector businesses in the UK 2000-2023\\nNumber of private sector businesses in the United Kingdom from 2000 to 2023 (in millions)\\nNumber of private sector businesses in the UK 2023, by sector\\nNumber of private sector businesses in the United Kingdom in 2023, by sector\\nNumber of businesses by enterprise size in the UK 2023\\nNumber of private sector businesses in the United Kingdom in 2023, by employment size\\nNumber of private sector businesses in the UK 2023, by region\\nNumber of private sector businesses in the United Kingdom in 2023, by region\\nNumber of local business units in the UK 2012-2023\\nNumber of local units in VAT and/or PAYE based enterprises in the United Kingdom from 2012 to 2023 (in millions)\\nBusiness investment index in the UK 1997-2023\\nBusiness investment index in the United Kingdom from 1st quarter 1997 to 2nd quarter 2023 (Q1 1997=100)\\nBusiness confidence Index in the UK 1977-2023\\nBusiness confidence Index of the United Kingdom from March 1977 to November 2023 (100 = long-term average)\\nRelated topics\\nRecommended\\nReport on the topic\\nKey figures\\nThe most important key figures provide you with a compact summary of the topic of \"The UK economy\" and take you straight to the corresponding statistics.\\n Monthly GDP growth of the UK 2020-2023\\nMonthly growth of gross domestic product in the United Kingdom from January 2020 to September 2023\\nGDP of the UK 2021, by region\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\nGDP growth rate in the UK 2021, by region\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\nGDP per capita of the UK 2021, by region\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\nGDP growth forecast for the UK 2000-2028\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\nLabor Market\\nLabor Market\\nUnemployment rate of the UK 1971-2023\\nUnemployment rate in the United Kingdom from March 1971 to September 2023\\nEmployment rate in the UK 1971-2022\\nEmployment rate in the United Kingdom from March 1971 to July 2023\\nNumber of people unemployed in the UK 1971-2023\\nNumber of people unemployed in the United Kingdom from March 1971 to July 2023 (in 1,000s)\\nNumber of people employed in the UK 1971-2021\\nNumber of people employed in the United Kingdom from March 1971 to July 2023 (in 1,000s)\\nUnemployment rate in the UK 1971-2023, by gender\\nUnemployment rate in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023, by gender\\nUnemployment rate in the UK 1992-2023, by age group\\nUnemployment rate in the United Kingdom from May 1992 to July 2023, by age group\\nYouth unemployment rate in the UK 1992-2023\\nYouth unemployment rate in the United Kingdom from May 1992 to July 2023\\nAverage annual earnings for full-time employees in the UK 1999-2023\\nMedian annual earnings for full-time employees in the United Kingdom from 1999 to 2023 (in nominal GBP)\\nAverage weekly earning growth in the UK 2001-2023\\nAverage year-on-year growth of weekly earnings (3 month average) in the United Kingdom from March 2001 to October 2023\\nNumber of redundancies in the UK 1995-2023\\nAverage number of people made redundant in the United Kingdom from May 1995 to July 2023 (in 1,000s)\\nOverall weekly hours worked in the UK 1971-2023\\nOverall weekly hours worked for all employees in the United Kingdom from 1st quarter 1971 to 2nd quarter 2023 (in million hours worked)\\n Transforming data into design:\\nStatista Content & Design\\nStrategy and business building for the data-driven economy:\\nThe UK economy - Statistics & Facts\\nUK households under pressure in 2023\\nCoronavirus devastates UK economy in 2020\\nKey insights\\nDetailed statistics\\nGDP of the UK 1948-2022\\nDetailed statistics\\nUnemployment rate of the UK 1971-2023\\nDetailed statistics\\nInflation rate in the UK 1989-2023\\nEditor’s Picks\\nCurrent statistics on this topic\\nCurrent statistics on this topic\\nWages & Salaries\\nAverage weekly earning growth in the UK 2001-2023\\nIncome & Expenditure\\nPublic sector spending in the United Kingdom 2023/24\\nEmployment\\nNumber of people employed in the UK 1971-2021\\nRelated topics\\nRecommended\\nRecommended statistics\\nGross domestic product\\nGross domestic product\\nGDP of the UK 1948-2022\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\nAnnual GDP growth in the UK 1949-2022\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\nGDP per capita in the UK 1955-2022\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\nQuarterly GDP of the UK 1955-2023\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 1955 to 3rd quarter 2023 (in million GBP)\\nQuarterly GDP growth of the UK 2015-2023\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2015 to 3rd quarter 2023\\nQuarterly GDP per capita in the UK 1955-2023\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 1955 to 3rd quarter 2023 (in GBP)\\nMonthly GDP of the UK 1997-2023\\nMonthly index of gross domestic product in the United Kingdom from January 1997 to September 2023 (2019=100)\\n GDP\\nAnnual GDP growth in the UK 1949-2022\\nQuarterly GDP per capita growth in the UK 2015-2023\\nMonthly GDP growth of the UK 2020-2023\\nGDP per capita in the UK 1955-2022\\nLabor market\\nNumber of people employed in the UK 1971-2021\\nNumber of people unemployed in the UK 1971-2023\\nDaily number of jobs furloughed in the UK 2020-2021\\nAverage annual earnings for full-time employees in the UK 1999-2023\\nForecasts for 2023\\nGDP growth forecast for the UK 2000-2028\\nAnnual unemployment rate in the UK 2000-2028\\nCPI annual inflation rate UK 2000-2028\\nRPI annual inflation rate UK 2000-2028\\n Industry Overview\\nDigital & Trend reports\\nOverview and forecasts on trending topics\\nIndustry & Market reports\\nIndustry and market insights and forecasts\\nCompanies & Products reports\\nKey figures and rankings about companies and products\\nConsumer & Brand reports\\nConsumer and brand insights and preferences in various industries\\nPolitics & Society reports\\nDetailed information about political and social topics\\nCountry & Region reports\\nAll key figures about countries and regions\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\nInsights on consumer attitudes and behavior worldwide\\nBusiness information on 100m+ public and private companies\\nExplore Company Insights\\nDetailed information for 39,000+ online stores and marketplaces\\nDirectly accessible data for 170 industries from 150+ countries\\nand over 1\\xa0Mio. facts.\\n', 'score': 0.********, 'raw_content': None}, {'title': 'GDP growth (annual %) - United Kingdom | Data - World Bank Data', 'url': 'https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB&most_recent_value_desc=false', 'content': 'GDP growth (annual %) - United Kingdom | Data Data GDP growth (annual %)United Kingdom Data Catalog Data Programs International Debt Statistics Other Books and Reports For Developers GDP growth (annual %) - United Kingdom ====================================== Similar values Highest values Lowest values GDP (constant 2015 US$)  GDP (current US$)  GDP (constant LCU)  GDP: linked series (current LCU)  GDP, PPP (constant 2021 international $)  GDP (current LCU)  GDP, PPP (current international $)  GDP per capita growth (annual %)  Country Most Recent Value All Countries and Economies Country Most Recent Value This site uses cookies to optimize functionality and give you the best possible experience. If you continue to navigate this website beyond this page, cookies will be placed on your browser.', 'score': 0.7892337, 'raw_content': None}], 'response_time': 2.3}), AIMessage(content=[{'text': 'Let me search for more specific data.', 'type': 'text'}, {'id': 'toolu_019dPRXojLJoVNYFLzzSWw4w', 'input': {'query': 'UK GDP values by year 2019 2020 2021 2022 2023'}, 'name': 'tavily_search_results_json', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_01Ac9vcTFneb5dvcEYXJyf1P', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 5890, 'output_tokens': 87}}, id='run-3504417f-c0b5-4908-82e2-89a18abb1b8e-0', tool_calls=[{'name': 'tavily_search_results_json', 'args': {'query': 'UK GDP values by year 2019 2020 2021 2022 2023'}, 'id': 'toolu_019dPRXojLJoVNYFLzzSWw4w', 'type': 'tool_call'}], usage_metadata={'input_tokens': 5890, 'output_tokens': 87, 'total_tokens': 5977, 'input_token_details': {}}), ToolMessage(content='[{\"url\": \"https://www.macrotrends.net/global-metrics/countries/GBR/united-kingdom/gdp-gross-domestic-product\", \"content\": \"U.K. gdp for 2023 was $3,340.03B, a 8.13% increase from 2022. U.K. gdp for 2022 was $3,088.84B, a 1.68% decline from 2021. U.K. gdp for 2021 was $3,141.51B, a 16.45% increase from 2020. U.K. gdp for 2020 was $2,697.81B, a 5.39% decline from 2019.\"}, {\"url\": \"https://countryeconomy.com/gdp/uk?year=2023\", \"content\": \"Gross Domestic Product of United Kingdom grew 0.3% in 2023 compared to last year. This rate is 45 -tenths of one percent less than the figure of 4.8% published in 2022. The GDP figure in 2023 was $3,380,855 million, leaving United Kingdom placed 6th in the ranking of GDP of the 196 countries that we publish.\"}, {\"url\": \"https://www.statista.com/topics/3795/gdp-of-the-uk/\", \"content\": \"Monthly growth of gross domestic product in the United Kingdom from January 2019 to November 2023\\\\nContribution to GDP growth in the UK 2023, by sector\\\\nContribution to gross domestic product growth in the United Kingdom in January 2023, by sector\\\\nGDP growth rate in the UK 1999-2021, by country\\\\nAnnual growth rates of gross domestic product in the United Kingdom from 1999 to 2021, by country\\\\nGDP growth rate in the UK 2021, by region\\\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\\\nGDP growth of Scotland 2021, by local area\\\\nAnnual growth rates of gross domestic product in Scotland in 2021, by local (ITL 3) area\\\\nGDP growth of Wales 2021, by local area\\\\nAnnual growth rates of gross domestic product in Wales in 2021, by local (ITL 3) area\\\\nGDP growth of Northern Ireland 2021, by local area\\\\nAnnual growth rates of gross domestic product in Northern Ireland in 2021, by local (ITL 3) area\\\\nGDP per capita\\\\nGDP per capita\\\\nGDP per capita in the UK 1955-2022\\\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\\\nAnnual GDP per capita growth in the UK 1956-2022\\\\nAnnual GDP per capita growth in the United Kingdom from 1956 to 2022\\\\nQuarterly GDP per capita in the UK 2019-2023\\\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\\\nQuarterly GDP per capita growth in the UK 2019-2023\\\\nQuarterly GDP per capita growth in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\\\nGDP per capita of the UK 1999-2021, by country\\\\nGross domestic product per capita of the United Kingdom from 1999 to 2021, by country (in GBP)\\\\nGDP per capita of the UK 2021, by region\\\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\\\nGlobal Comparisons\\\\nGlobal Comparisons\\\\nCountries with the largest gross domestic product (GDP) 2022\\\\n Monthly GDP of the UK 2019-2023\\\\nMonthly index of gross domestic product in the United Kingdom from January 2019 to November 2023 (2019=100)\\\\nGVA of the UK 2022, by sector\\\\nGross value added of the United Kingdom in 2022, by industry sector (in million GBP)\\\\nGDP of the UK 2021, by country\\\\nGross domestic product of the United Kingdom in 2021, by country (in million GBP)\\\\nGDP of the UK 2021, by region\\\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\\\nGDP of Scotland 2021, by local area\\\\nGross domestic product of Scotland in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP of Wales 2021, by local area\\\\nGross domestic product of Wales in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP of Northern Ireland 2021, by local area\\\\nGross domestic product of Northern Ireland in 2021, by local (ITL 3) area (in million GBP)\\\\nGDP growth\\\\nGDP growth\\\\nGDP growth forecast for the UK 2000-2028\\\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\\\nAnnual GDP growth in the UK 1949-2022\\\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\\\nQuarterly GDP growth of the UK 2019-2023\\\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023\\\\nMonthly GDP growth of the UK 2019-2023\\\\n Transforming data into design:\\\\nStatista Content & Design\\\\nStrategy and business building for the data-driven economy:\\\\nUK GDP - Statistics & Facts\\\\nUK economy expected to shrink in 2023\\\\nCharacteristics of UK GDP\\\\nKey insights\\\\nDetailed statistics\\\\nGDP of the UK 1948-2022\\\\nDetailed statistics\\\\nAnnual GDP growth in the UK 1949-2022\\\\nDetailed statistics\\\\nGDP per capita in the UK 1955-2022\\\\nEditor’s Picks\\\\nCurrent statistics on this topic\\\\nCurrent statistics on this topic\\\\nKey Economic Indicators\\\\nMonthly GDP growth of the UK 2019-2023\\\\nKey Economic Indicators\\\\nMonthly GDP of the UK 2019-2023\\\\nKey Economic Indicators\\\\nContribution to GDP growth in the UK 2023, by sector\\\\nRelated topics\\\\nRecommended\\\\nRecommended statistics\\\\nGDP\\\\nGDP\\\\nGDP of the UK 1948-2022\\\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\\\nQuarterly GDP of the UK 2019-2023\\\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in million GBP)\\\\n The 20 countries with the largest gross domestic product (GDP) in 2022 (in billion U.S. dollars)\\\\nGDP of European countries in 2022\\\\nGross domestic product at current market prices of selected European countries in 2022 (in million euros)\\\\nReal GDP growth rates in Europe 2023\\\\nAnnual real gross domestic product (GDP) growth rate in European countries in 2023\\\\nGross domestic product (GDP) of Europe\\'s largest economies 1980-2028\\\\nGross domestic product (GDP) at current prices of Europe\\'s largest economies from 1980 to 2028 (in billion U.S dollars)\\\\nUnited Kingdom\\'s share of global gross domestic product (GDP) 2028\\\\nUnited Kingdom (UK): Share of global gross domestic product (GDP) adjusted for Purchasing Power Parity (PPP) from 2018 to 2028\\\\nRelated topics\\\\nRecommended\\\\nReport on the topic\\\\nKey figures\\\\nThe most important key figures provide you with a compact summary of the topic of \\\\\"UK GDP\\\\\" and take you straight to the corresponding statistics.\\\\n Industry Overview\\\\nDigital & Trend reports\\\\nOverview and forecasts on trending topics\\\\nIndustry & Market reports\\\\nIndustry and market insights and forecasts\\\\nCompanies & Products reports\\\\nKey figures and rankings about companies and products\\\\nConsumer & Brand reports\\\\nConsumer and brand insights and preferences in various industries\\\\nPolitics & Society reports\\\\nDetailed information about political and social topics\\\\nCountry & Region reports\\\\nAll key figures about countries and regions\\\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\\\nInsights on consumer attitudes and behavior worldwide\\\\nBusiness information on 100m+ public and private companies\\\\nExplore Company Insights\\\\nDetailed information for 39,000+ online stores and marketplaces\\\\nDirectly accessible data for 170 industries from 150+ countries\\\\nand over 1\\xa0Mio. facts.\\\\n\"}, {\"url\": \"https://www.ons.gov.uk/economy/grossdomesticproductgdp/compendium/unitedkingdomnationalaccountsthebluebook/2024/nationalaccountsataglance\", \"content\": \"Real gross domestic product (GDP) is estimated to have increased by 0.3% in 2023, following a recovery from the impacts of the coronavirus (COVID-19) pandemic over the two previous years (Figure 1). Data for the UK are the Office for National Statistics (ONS) measure of real gross domestic product (GDP). Figure 9: Real GDP per head fell in 2023 when compared with 2022 in six G10 economies, including the UK Data for the UK are the Office for National Statistics (ONS) measure of real gross domestic product (GDP) per head. Download this chart Figure 9: Real GDP per head fell in 2023 when compared with 2022 in six G10 economies, including the UK\"}, {\"url\": \"https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB&most_recent_value_desc=false\", \"content\": \"GDP growth (annual %) - United Kingdom | Data Data GDP growth (annual %)United Kingdom Data Catalog Data Programs International Debt Statistics Other Books and Reports For Developers GDP growth (annual %) - United Kingdom ====================================== Similar values Highest values Lowest values GDP (constant 2015 US$)  GDP (current US$)  GDP (constant LCU)  GDP: linked series (current LCU)  GDP, PPP (constant 2021 international $)  GDP (current LCU)  GDP, PPP (current international $)  GDP per capita growth (annual %)  Country Most Recent Value All Countries and Economies Country Most Recent Value This site uses cookies to optimize functionality and give you the best possible experience. If you continue to navigate this website beyond this page, cookies will be placed on your browser.\"}]', name='tavily_search_results_json', id='84c571ca-27c6-4023-93a2-f0c2e8b6abb0', tool_call_id='toolu_019dPRXojLJoVNYFLzzSWw4w', artifact={'query': 'UK GDP values by year 2019 2020 2021 2022 2023', 'follow_up_questions': None, 'answer': None, 'images': [], 'results': [{'title': 'U.K. GDP 1960-2024 - Macrotrends', 'url': 'https://www.macrotrends.net/global-metrics/countries/GBR/united-kingdom/gdp-gross-domestic-product', 'content': 'U.K. gdp for 2023 was $3,340.03B, a 8.13% increase from 2022. U.K. gdp for 2022 was $3,088.84B, a 1.68% decline from 2021. U.K. gdp for 2021 was $3,141.51B, a 16.45% increase from 2020. U.K. gdp for 2020 was $2,697.81B, a 5.39% decline from 2019.', 'score': 0.9974491, 'raw_content': None}, {'title': 'United Kingdom (UK) GDP - Gross Domestic Product 2023', 'url': 'https://countryeconomy.com/gdp/uk?year=2023', 'content': 'Gross Domestic Product of United Kingdom grew 0.3% in 2023 compared to last year. This rate is 45 -tenths of one percent less than the figure of 4.8% published in 2022. The GDP figure in 2023 was $3,380,855 million, leaving United Kingdom placed 6th in the ranking of GDP of the 196 countries that we publish.', 'score': 0.9964064, 'raw_content': None}, {'title': 'UK GDP - Statistics & Facts | Statista', 'url': 'https://www.statista.com/topics/3795/gdp-of-the-uk/', 'content': 'Monthly growth of gross domestic product in the United Kingdom from January 2019 to November 2023\\nContribution to GDP growth in the UK 2023, by sector\\nContribution to gross domestic product growth in the United Kingdom in January 2023, by sector\\nGDP growth rate in the UK 1999-2021, by country\\nAnnual growth rates of gross domestic product in the United Kingdom from 1999 to 2021, by country\\nGDP growth rate in the UK 2021, by region\\nAnnual growth rates of gross domestic product in the United Kingdom in 2021, by region\\nGDP growth of Scotland 2021, by local area\\nAnnual growth rates of gross domestic product in Scotland in 2021, by local (ITL 3) area\\nGDP growth of Wales 2021, by local area\\nAnnual growth rates of gross domestic product in Wales in 2021, by local (ITL 3) area\\nGDP growth of Northern Ireland 2021, by local area\\nAnnual growth rates of gross domestic product in Northern Ireland in 2021, by local (ITL 3) area\\nGDP per capita\\nGDP per capita\\nGDP per capita in the UK 1955-2022\\nGross domestic product per capita in the United Kingdom from 1955 to 2022 (in GBP)\\nAnnual GDP per capita growth in the UK 1956-2022\\nAnnual GDP per capita growth in the United Kingdom from 1956 to 2022\\nQuarterly GDP per capita in the UK 2019-2023\\nQuarterly GDP per capita in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\nQuarterly GDP per capita growth in the UK 2019-2023\\nQuarterly GDP per capita growth in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in GBP)\\nGDP per capita of the UK 1999-2021, by country\\nGross domestic product per capita of the United Kingdom from 1999 to 2021, by country (in GBP)\\nGDP per capita of the UK 2021, by region\\nGross domestic product per capita of the United Kingdom in 2021, by region (in GBP)\\nGlobal Comparisons\\nGlobal Comparisons\\nCountries with the largest gross domestic product (GDP) 2022\\n Monthly GDP of the UK 2019-2023\\nMonthly index of gross domestic product in the United Kingdom from January 2019 to November 2023 (2019=100)\\nGVA of the UK 2022, by sector\\nGross value added of the United Kingdom in 2022, by industry sector (in million GBP)\\nGDP of the UK 2021, by country\\nGross domestic product of the United Kingdom in 2021, by country (in million GBP)\\nGDP of the UK 2021, by region\\nGross domestic product of the United Kingdom in 2021, by region (in million GBP)\\nGDP of Scotland 2021, by local area\\nGross domestic product of Scotland in 2021, by local (ITL 3) area (in million GBP)\\nGDP of Wales 2021, by local area\\nGross domestic product of Wales in 2021, by local (ITL 3) area (in million GBP)\\nGDP of Northern Ireland 2021, by local area\\nGross domestic product of Northern Ireland in 2021, by local (ITL 3) area (in million GBP)\\nGDP growth\\nGDP growth\\nGDP growth forecast for the UK 2000-2028\\nForecasted annual growth of gross domestic product in the United Kingdom from 2000 to 2028\\nAnnual GDP growth in the UK 1949-2022\\nAnnual growth of gross domestic product in the United Kingdom from 1949 to 2022\\nQuarterly GDP growth of the UK 2019-2023\\nQuarterly growth of gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023\\nMonthly GDP growth of the UK 2019-2023\\n Transforming data into design:\\nStatista Content & Design\\nStrategy and business building for the data-driven economy:\\nUK GDP - Statistics & Facts\\nUK economy expected to shrink in 2023\\nCharacteristics of UK GDP\\nKey insights\\nDetailed statistics\\nGDP of the UK 1948-2022\\nDetailed statistics\\nAnnual GDP growth in the UK 1949-2022\\nDetailed statistics\\nGDP per capita in the UK 1955-2022\\nEditor’s Picks\\nCurrent statistics on this topic\\nCurrent statistics on this topic\\nKey Economic Indicators\\nMonthly GDP growth of the UK 2019-2023\\nKey Economic Indicators\\nMonthly GDP of the UK 2019-2023\\nKey Economic Indicators\\nContribution to GDP growth in the UK 2023, by sector\\nRelated topics\\nRecommended\\nRecommended statistics\\nGDP\\nGDP\\nGDP of the UK 1948-2022\\nGross domestic product of the United Kingdom from 1948 to 2022 (in million GBP)\\nQuarterly GDP of the UK 2019-2023\\nQuarterly gross domestic product in the United Kingdom from 1st quarter 2019 to 3rd quarter 2023 (in million GBP)\\n The 20 countries with the largest gross domestic product (GDP) in 2022 (in billion U.S. dollars)\\nGDP of European countries in 2022\\nGross domestic product at current market prices of selected European countries in 2022 (in million euros)\\nReal GDP growth rates in Europe 2023\\nAnnual real gross domestic product (GDP) growth rate in European countries in 2023\\nGross domestic product (GDP) of Europe\\'s largest economies 1980-2028\\nGross domestic product (GDP) at current prices of Europe\\'s largest economies from 1980 to 2028 (in billion U.S dollars)\\nUnited Kingdom\\'s share of global gross domestic product (GDP) 2028\\nUnited Kingdom (UK): Share of global gross domestic product (GDP) adjusted for Purchasing Power Parity (PPP) from 2018 to 2028\\nRelated topics\\nRecommended\\nReport on the topic\\nKey figures\\nThe most important key figures provide you with a compact summary of the topic of \"UK GDP\" and take you straight to the corresponding statistics.\\n Industry Overview\\nDigital & Trend reports\\nOverview and forecasts on trending topics\\nIndustry & Market reports\\nIndustry and market insights and forecasts\\nCompanies & Products reports\\nKey figures and rankings about companies and products\\nConsumer & Brand reports\\nConsumer and brand insights and preferences in various industries\\nPolitics & Society reports\\nDetailed information about political and social topics\\nCountry & Region reports\\nAll key figures about countries and regions\\nMarket forecast and expert KPIs for 1000+ markets in 190+ countries & territories\\nInsights on consumer attitudes and behavior worldwide\\nBusiness information on 100m+ public and private companies\\nExplore Company Insights\\nDetailed information for 39,000+ online stores and marketplaces\\nDirectly accessible data for 170 industries from 150+ countries\\nand over 1\\xa0Mio. facts.\\n', 'score': 0.********, 'raw_content': None}, {'title': 'National accounts at a glance - Office for National Statistics', 'url': 'https://www.ons.gov.uk/economy/grossdomesticproductgdp/compendium/unitedkingdomnationalaccountsthebluebook/2024/nationalaccountsataglance', 'content': 'Real gross domestic product (GDP) is estimated to have increased by 0.3% in 2023, following a recovery from the impacts of the coronavirus (COVID-19) pandemic over the two previous years (Figure 1). Data for the UK are the Office for National Statistics (ONS) measure of real gross domestic product (GDP). Figure 9: Real GDP per head fell in 2023 when compared with 2022 in six G10 economies, including the UK Data for the UK are the Office for National Statistics (ONS) measure of real gross domestic product (GDP) per head. Download this chart Figure 9: Real GDP per head fell in 2023 when compared with 2022 in six G10 economies, including the UK', 'score': 0.975249, 'raw_content': None}, {'title': 'GDP growth (annual %) - United Kingdom | Data - World Bank Data', 'url': 'https://data.worldbank.org/indicator/NY.GDP.MKTP.KD.ZG?locations=GB&most_recent_value_desc=false', 'content': 'GDP growth (annual %) - United Kingdom | Data Data GDP growth (annual %)United Kingdom Data Catalog Data Programs International Debt Statistics Other Books and Reports For Developers GDP growth (annual %) - United Kingdom ====================================== Similar values Highest values Lowest values GDP (constant 2015 US$)  GDP (current US$)  GDP (constant LCU)  GDP: linked series (current LCU)  GDP, PPP (constant 2021 international $)  GDP (current LCU)  GDP, PPP (current international $)  GDP per capita growth (annual %)  Country Most Recent Value All Countries and Economies Country Most Recent Value This site uses cookies to optimize functionality and give you the best possible experience. If you continue to navigate this website beyond this page, cookies will be placed on your browser.', 'score': 0.********, 'raw_content': None}], 'response_time': 2.37}), HumanMessage(content='Based on the search results, I can provide the UK\\'s GDP values for the past 5 years (in billions of US dollars):\\n\\n2019: $2,851.54\\n2020: $2,697.81\\n2021: $3,141.51\\n2022: $3,088.84\\n2023: $3,340.03\\n\\nI\\'ll pass this data to my chart generator colleague to create a line chart. They should create a line chart with:\\n- Years 2019-2023 on the x-axis\\n- GDP values in billions USD on the y-axis\\n- Title: \"UK GDP 2019-2023\"\\n- Clear data points showing the values\\n\\nOver to you, chart generator colleague!', additional_kwargs={}, response_metadata={}, name='researcher', id='7e790b7a-7b06-4b45-a595-8736b53db844'), AIMessage(content=[{'text': \"I'll create a line chart with the specified GDP data and requirements using Python and matplotlib.\", 'type': 'text'}, {'id': 'toolu_017HmYWRMpnhPaw3SamZCQua', 'input': {'code': \"import matplotlib.pyplot as plt\\n\\nyears = [2019, 2020, 2021, 2022, 2023]\\ngdp = [2851.54, 2697.81, 3141.51, 3088.84, 3340.03]\\n\\nplt.figure(figsize=(10, 6))\\nplt.plot(years, gdp, marker='o', linewidth=2, markersize=8)\\n\\nplt.title('UK GDP 2019-2023', pad=15, size=14)\\nplt.xlabel('Year', labelpad=10)\\nplt.ylabel('GDP (Billions USD)', labelpad=10)\\n\\n# Add value labels above each point\\nfor i, value in enumerate(gdp):\\n    plt.text(years[i], value + 30, f'${value}B', ha='center')\\n\\nplt.grid(True, linestyle='--', alpha=0.7)\\nplt.show()\"}, 'name': 'python_repl_tool', 'type': 'tool_use'}], additional_kwargs={}, response_metadata={'id': 'msg_01Y29F46KJQzTmefwQL6s9Dp', 'model': 'claude-3-5-sonnet-20241022', 'stop_reason': 'tool_use', 'stop_sequence': None, 'usage': {'input_tokens': 8744, 'output_tokens': 295}}, id='run-e0ee838e-1c18-46d9-bed7-459330376276-0', tool_calls=[{'name': 'python_repl_tool', 'args': {'code': \"import matplotlib.pyplot as plt\\n\\nyears = [2019, 2020, 2021, 2022, 2023]\\ngdp = [2851.54, 2697.81, 3141.51, 3088.84, 3340.03]\\n\\nplt.figure(figsize=(10, 6))\\nplt.plot(years, gdp, marker='o', linewidth=2, markersize=8)\\n\\nplt.title('UK GDP 2019-2023', pad=15, size=14)\\nplt.xlabel('Year', labelpad=10)\\nplt.ylabel('GDP (Billions USD)', labelpad=10)\\n\\n# Add value labels above each point\\nfor i, value in enumerate(gdp):\\n    plt.text(years[i], value + 30, f'${value}B', ha='center')\\n\\nplt.grid(True, linestyle='--', alpha=0.7)\\nplt.show()\"}, 'id': 'toolu_017HmYWRMpnhPaw3SamZCQua', 'type': 'tool_call'}], usage_metadata={'input_tokens': 8744, 'output_tokens': 295, 'total_tokens': 9039, 'input_token_details': {}}), ToolMessage(content=\"Successfully executed:\\n```python\\nimport matplotlib.pyplot as plt\\n\\nyears = [2019, 2020, 2021, 2022, 2023]\\ngdp = [2851.54, 2697.81, 3141.51, 3088.84, 3340.03]\\n\\nplt.figure(figsize=(10, 6))\\nplt.plot(years, gdp, marker='o', linewidth=2, markersize=8)\\n\\nplt.title('UK GDP 2019-2023', pad=15, size=14)\\nplt.xlabel('Year', labelpad=10)\\nplt.ylabel('GDP (Billions USD)', labelpad=10)\\n\\n# Add value labels above each point\\nfor i, value in enumerate(gdp):\\n    plt.text(years[i], value + 30, f'${value}B', ha='center')\\n\\nplt.grid(True, linestyle='--', alpha=0.7)\\nplt.show()\\n```\\nStdout: \\n\\nIf you have completed all tasks, respond with FINAL ANSWER.\", name='python_repl_tool', id='5421128d-9996-4dc5-b14c-77b862912d94', tool_call_id='toolu_017HmYWRMpnhPaw3SamZCQua'), HumanMessage(content='FINAL ANSWER: I have created a line chart showing the UK\\'s GDP from 2019 to 2023. The chart includes:\\n- A clear line with marked data points\\n- Years on the x-axis\\n- GDP values in billions USD on the y-axis\\n- Value labels above each data point\\n- A grid for better readability\\n- The title \"UK GDP 2019-2023\"\\n\\nThe chart clearly shows the GDP drop in 2020 due to the pandemic, followed by recovery and growth through 2023, with the most recent value reaching $3,340.03 billion.', additional_kwargs={}, response_metadata={}, name='chart_generator', id='4a649455-eed8-4b4f-a19f-c172140430c3')]}}\n", "----\n"]}], "source": ["events = graph.stream(\n", "    {\n", "        \"messages\": [\n", "            (\n", "                \"user\",\n", "                \"First, get the UK's GDP over the past 5 years, then make a line chart of it. \"\n", "                \"Once you make the chart, finish.\",\n", "            )\n", "        ],\n", "    },\n", "    # Maximum number of steps to take in the graph\n", "    {\"recursion_limit\": 150},\n", ")\n", "for s in events:\n", "    print(s)\n", "    print(\"----\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 5}