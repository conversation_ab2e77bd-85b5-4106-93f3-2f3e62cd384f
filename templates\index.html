<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数学计算系统 - MCP + LangGraph</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 头部导航 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-calculator me-2"></i>
                    数学计算系统
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text">
                        <i class="fas fa-robot me-1"></i>
                        基于 MCP + LangGraph
                    </span>
                </div>
            </div>
        </nav>

        <div class="row">
            <!-- 左侧工具栏 -->
            <div class="col-md-3">
                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-tools me-2"></i>可用工具</h5>
                    </div>
                    <div class="card-body">
                        <div id="tools-list">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                                <small class="d-block mt-2">加载中...</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-3">
                    <div class="card-header">
                        <h5><i class="fas fa-lightbulb me-2"></i>计算示例</h5>
                    </div>
                    <div class="card-body">
                        <div id="examples-list">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                                <small class="d-block mt-2">加载中...</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-info-circle me-2"></i>系统状态</h5>
                    </div>
                    <div class="card-body">
                        <div id="system-status">
                            <div class="text-center">
                                <div class="spinner-border spinner-border-sm" role="status"></div>
                                <small class="d-block mt-2">检查中...</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-comments me-2"></i>数学计算对话</h5>
                        <button class="btn btn-sm btn-outline-secondary float-end" onclick="clearChat()">
                            <i class="fas fa-trash me-1"></i>清空对话
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- 对话历史 -->
                        <div id="chat-history" class="chat-container mb-3">
                            <div class="message assistant-message">
                                <div class="message-content">
                                    <i class="fas fa-robot me-2"></i>
                                    您好！我是数学计算助手。我可以帮您进行各种数学运算，包括基础运算、高级运算、方程求解等。请告诉我您想要计算什么！
                                </div>
                                <div class="message-time">
                                    <small class="text-muted">系统消息</small>
                                </div>
                            </div>
                        </div>

                        <!-- 输入区域 -->
                        <div class="input-group">
                            <input type="text" 
                                   id="question-input" 
                                   class="form-control" 
                                   placeholder="请输入您的数学问题，例如：计算 15 + 27"
                                   onkeypress="handleKeyPress(event)">
                            <button class="btn btn-primary" onclick="sendQuestion()" id="send-btn">
                                <i class="fas fa-paper-plane me-1"></i>发送
                            </button>
                        </div>

                        <!-- 快捷按钮 -->
                        <div class="mt-3">
                            <small class="text-muted">快捷操作：</small>
                            <div class="btn-group-sm mt-1">
                                <button class="btn btn-outline-secondary btn-sm me-1" onclick="insertText('+ ')">+</button>
                                <button class="btn btn-outline-secondary btn-sm me-1" onclick="insertText('- ')">-</button>
                                <button class="btn btn-outline-secondary btn-sm me-1" onclick="insertText('× ')">×</button>
                                <button class="btn btn-outline-secondary btn-sm me-1" onclick="insertText('÷ ')">÷</button>
                                <button class="btn btn-outline-secondary btn-sm me-1" onclick="insertText('^')">^</button>
                                <button class="btn btn-outline-secondary btn-sm me-1" onclick="insertText('√')">√</button>
                                <button class="btn btn-outline-secondary btn-sm me-1" onclick="insertText('!')">!</button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="insertText('%')">%</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义 JavaScript -->
    <script src="/static/app.js"></script>
</body>
</html>
