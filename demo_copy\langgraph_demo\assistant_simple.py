import os
import asyncio
from typing import Literal
from langchain_core.messages import HumanMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, StateGraph, MessagesState
from langgraph.prebuilt import ToolNode
from config import <PERSON><PERSON>EL_CONFIG, LANGSMITH_CONFIG
# from langchain_core.prompts import ChatPromptTemplate
from langchain_mcp_adapters.client import MultiServerMCPClient

# 设置 LangSmith 环境变量
os.environ["LANGCHAIN_TRACING_V2"] = LANGSMITH_CONFIG["tracing_v2"]
os.environ["LANGCHAIN_API_KEY"] = LANGSMITH_CONFIG["api_key"]
os.environ["LANGCHAIN_ENDPOINT"] = LANGSMITH_CONFIG["endpoint"]
if "project" in LANGSMITH_CONFIG:
    os.environ["LANGCHAIN_PROJECT"] = LANGSMITH_CONFIG["project"]

mcp_servers = {
  "firecrawl-mcp": { # 第三方 mcp 服务
    "command": "npx",
    "args": ["-y", "firecrawl-mcp"],
    "env": {
      "FIRECRAWL_API_KEY": "fc-af1b3ac1a0c2402485402fd0e34da158"
    },
    "transport": "stdio"
  },
  'mcp_demo': { # 自定义 mcp 服务
    'command': 'python',
    'args': ['server.py'],
    'cwd': '../mcp_demo',
    'transport': 'stdio'
  }
}

client = MultiServerMCPClient(mcp_servers)
mcp_tools = asyncio.run(client.get_tools())

# 定义工具函数
@tool
def get_weather(query: str):
  """模拟一个查询天气的工具"""
  print("[tool calling] 调用 get_weather 工具")
  if "杭州" in query.lower() or "Hangzhou" in query.lower():
    return "现在是30度，晴。"
  return "现在是20度，大雨。"

# 将工具函数放入工具列表
tools = [get_weather] + mcp_tools

# 创建工具节点
tool_node = ToolNode(tools)

# 1. 初始化模型和工具，定义并绑定工具到模型
model = ChatOpenAI(
  model=MODEL_CONFIG["model_name"],
  openai_api_base=MODEL_CONFIG["base_url"],
  openai_api_key=MODEL_CONFIG["api_key"],
  temperature=MODEL_CONFIG["temperature"]
).bind_tools(tools)

# 定义函数，决定是否继续执行
def should_continue(state: MessagesState) -> Literal["tools", END]:
  messages = state["messages"]
  last_message = messages[-1]
  # 如果LLM调用了工具，则转到 tools 节点
  if last_message.tool_calls:
    return "tools"
  # 否则，停止，回复用户
  return END

# 定义调用模型的函数
async def call_model(state: MessagesState):
  messages = state["messages"]
  response = await model.ainvoke(messages)
  # 返回列表，因为这将被添加到现有列表中
  return {"messages": [response]}

# 2. 用状态（MessageState）初始化图(StateGraph)，定义一个新的状态图
# MessageState 是一个预构建的状态模式，具有一个属性（一个LangChain Message 对象列表），以及将每个节点的更新合并到状态中的逻辑
workflow = StateGraph(MessagesState)

# 3. 定义2个图节点
# agent 节点：决定采取什么行动
# tools 节点：如果 agent 决定采取行动，此节点将执行该行动
workflow.add_node("agent", call_model)
workflow.add_node("tools", tool_node)

# 4. 定义入口节点和边
# 设置图执行的入口节点为“agent”，表示这是第一个被调用的节点
workflow.set_entry_point("agent")
# 添加条件边，边就是从一个节点到另一个节点，以下这条边意味着调用节点 agent 后采取的行动，行动后再调用下一个节点（tools 或 END）
workflow.add_conditional_edges(
  "agent",
  should_continue
)

# 添加从 tools 到 agent 的普通边
# 这意味着调用 tools 后，接下来调用 agent 节点
# 注意：
#   1）条件边：目的地取决于图状态（MessageState）的内容，比如 should_continue 根据不同条件返回不同的节点
#   2）普通边：调用工具后，图应该始终返回到代理以决定下一步操作
workflow.add_edge("tools", "agent")

# 初始化内存以在图运行时持久化状态
checkpointer = MemorySaver()

# 5. 编译图
# 编译成一个 LangChain 可运行对象 => 编译图时，会将其转换为 LangChain Runnable，所以这之后我们可以使用 .invoke()、.strean() 和 .batch()
# 这意味着你可以像使用其他运行对象一样使用它
# 在编译图时可以选择传递检查点对象，以在图运行时持久化状态，并启用内存、“人机交互”工作流、时间旅行等等
app = workflow.compile(checkpointer=checkpointer)

# 6. 执行图，使用可运行对象
# LangGraph 将输入消息添加到内部状态 -> 将状态传递给入口节点 agent -> agent 节点执行，调用聊天模型 -> 聊天模型返回 AIMessage -> 
# LangGraph 将其添加到状态中 -> 图循环以上步骤，直到 AIMessage 上不再有 tool_calls -> 执行到 END 节点，输出最终状态
async def main():
  # 测试1
  content="杭州天气怎么样？"
  print("Human：", content)
  final_state = await app.ainvoke(
    {"messages": [HumanMessage(content)]},
    config={"configurable": {"thread_id": 42}, "run_name": LANGSMITH_CONFIG["project"]}
  )
  # 从 final_state 中获取最后一条消息的内容
  result = final_state["messages"][-1].content
  print("Assistant：", result)

  # 测试2
  content="我问的哪个城市？"
  print("Human：", content)
  final_state = await app.ainvoke(
    {"messages": [HumanMessage(content)]},
    config={"configurable": {"thread_id": 42}} # 传递相同的 thread_id 时，对话上下文将通过保存的状态（即存储的消息列表）保留下来
  )
  result = final_state["messages"][-1].content
  print("Assistant：", result)

  # 测试3
  content="https://github.com/orgs/QwenLM/repositories 提取这一页的Markdown 文档，统计每个项目stars数；然后把所有stars数目相加，输出结果。如果抓取失败，直接计算1加2等于多少。"
  # content="1加2等于多少"
  print("Human：", content)
  final_state = await app.ainvoke(
    {"messages": [HumanMessage(content)]},
    config={"configurable": {"thread_id": 42}} # 传递相同的 thread_id 时，对话上下文将通过保存的状态（即存储的消息列表）保留下来
  )
  result = final_state["messages"][-1].content
  print("Assistant：", result)

if __name__ == "__main__":
  asyncio.run(main())