---
    name: CI / cd . / make spell_check
    
    on:
      push:
        branches: [main]
      pull_request:
        branches: [main]
    
    permissions:
      contents: read

    defaults:
      run:
        working-directory: docs

    jobs:
      codespell:
        name: (Check for spelling errors)
        runs-on: ubuntu-latest
    
        steps:
          - name: Checkout
            uses: actions/checkout@v4
    
          - name: Install Dependencies
            run: |
              pip install toml codespell==2.3.0 jupytext
    
          - name: Extract Ignore Words List
            run: |
              # Use a Python script to extract the ignore words list from pyproject.toml
              python ../.github/workflows/extract_ignored_words_list.py
            id: extract_ignore_words
    
          - name: Codespell
            uses: codespell-project/actions-codespell@v2.1
            with:
              skip: '*.ambr,*.lock,*.ipynb,*.yaml,*.zlib,*.css.map,*.js.map'
              ignore_words_list: ${{ steps.extract_ignore_words.outputs.ignore_words_list }}
          # We do this to avoid spellchecking cell outputs
          - name: Codespell Notebooks
            run: make codespell
          
          - name: Codespell LangGraph Library
            run: |
              # Change to root directory to check the main LangGraph library
              cd ..
              codespell --skip="*.ambr,*.lock,*.ipynb,*.yaml,*.zlib,*.css.map,*.js.map,*.pyc,__pycache__/*" --ignore-words-list="${{ steps.extract_ignore_words.outputs.ignore_words_list }}" libs/langgraph/langgraph/