{"cells": [{"attachments": {"67b615fe-0c25-4410-9d58-835982547001.png": {"image/png": "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"}}, "cell_type": "markdown", "id": "16dc0e41-80bd-4453-b421-dcf315741bf4", "metadata": {}, "source": ["# Code generation with RAG and self-correction\n", "\n", "AlphaCodium presented an approach for code generation that uses control flow.\n", "\n", "Main idea: [construct an answer to a coding question iteratively.](https://x.com/karpathy/status/1748043513156272416?s=20). \n", "\n", "[AlphaCodium](https://github.com/Codium-ai/AlphaCodium) iteravely tests and improves an answer on public and AI-generated tests for a particular question. \n", "\n", "We will implement some of these ideas from scratch using [LangGraph](https://langchain-ai.github.io/langgraph/):\n", "\n", "1. We start with a set of documentation specified by a user\n", "2. We use a long context LLM to ingest it and perform RAG to answer a question based upon it\n", "3. We will invoke a tool to produce a structured output\n", "4. We will perform two unit tests (check imports and code execution) prior returning the solution to the user \n", "\n", "![Screenshot 2024-05-23 at 2.17.42 PM.png](attachment:67b615fe-0c25-4410-9d58-835982547001.png)"]}, {"cell_type": "markdown", "id": "95a34aa2", "metadata": {}, "source": ["## Setup\n", "\n", "First, let's install our required packages and set the API keys we will need"]}, {"cell_type": "code", "execution_count": null, "id": "e3900420", "metadata": {}, "outputs": [], "source": ["! pip install -U langchain_community langchain-openai langchain-anthropic langchain langgraph bs4"]}, {"cell_type": "code", "execution_count": null, "id": "602be48f", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")\n", "_set_env(\"ANTHROPIC_API_KEY\")"]}, {"cell_type": "markdown", "id": "0963fd21", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "38330223-d8c8-4156-82b6-93e63343bc01", "metadata": {}, "source": ["## Docs\n", "\n", "Load [LangChain Expression Language](https://python.langchain.com/docs/concepts/#langchain-expression-language-lcel) (LCEL) docs as an example."]}, {"cell_type": "code", "execution_count": 1, "id": "c2eb35d1-4990-47dc-a5c4-208bae588a82", "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup as Soup\n", "from langchain_community.document_loaders.recursive_url_loader import RecursiveUrlLoader\n", "\n", "# LCEL docs\n", "url = \"https://python.langchain.com/docs/concepts/lcel/\"\n", "loader = RecursiveUrlLoader(\n", "    url=url, max_depth=20, extractor=lambda x: Soup(x, \"html.parser\").text\n", ")\n", "docs = loader.load()\n", "\n", "# Sort the list based on the URLs and get the text\n", "d_sorted = sorted(docs, key=lambda x: x.metadata[\"source\"])\n", "d_reversed = list(reversed(d_sorted))\n", "concatenated_content = \"\\n\\n\\n --- \\n\\n\\n\".join(\n", "    [doc.page_content for doc in d_reversed]\n", ")"]}, {"cell_type": "markdown", "id": "662d4ff4-1709-412f-bfed-5eb2b8d3d3dc", "metadata": {}, "source": ["## LLMs\n", "\n", "### Code solution\n", "\n", "First, we will try OpenAI and [Claude3](https://python.langchain.com/docs/integrations/providers/anthropic/) with function calling.\n", "\n", "We will create a `code_gen_chain` w/ either OpenAI or Claude and test them here."]}, {"cell_type": "markdown", "id": "95944645-35bc-4798-8a27-c262b245a74c", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "    <p class=\"admonition-title\">Using Pydantic with <PERSON><PERSON><PERSON><PERSON></p>\n", "    <p>\n", "        This notebook uses Pydantic v2 <code>BaseModel</code>, which requires <code>langchain-core >= 0.3</code>. Using <code>langchain-core < 0.3</code> will result in errors due to mixing of Pydantic v1 and v2 <code>BaseModels</code>.\n", "    </p>\n", "</div>"]}, {"cell_type": "code", "execution_count": 5, "id": "3ba3df70-f6b4-4ea5-a210-e10944960bc6", "metadata": {}, "outputs": [{"data": {"text/plain": ["code(prefix='To build a Retrieval-Augmented Generation (RAG) chain in LCEL, you will need to set up a chain that combines a retriever and a language model (LLM). The retriever will fetch relevant documents based on a query, and the LLM will generate a response using the retrieved documents as context. Here’s how you can do it:', imports='from langchain_core.prompts import ChatPromptTemplate\\nfrom langchain_openai import ChatOpenAI\\nfrom langchain_core.output_parsers import StrOutputParser\\nfrom langchain_core.retrievers import MyRetriever', code='# Define the retriever\\nretriever = MyRetriever()  # Replace with your specific retriever implementation\\n\\n# Define the LLM model\\nmodel = ChatOpenAI(model=\"gpt-4\")\\n\\n# Create a prompt template for the LLM\\nprompt_template = ChatPromptTemplate.from_template(\"Given the following documents, answer the question: {question}\\nDocuments: {documents}\")\\n\\n# Create the RAG chain\\nrag_chain = prompt_template | retriever | model | StrOutputParser()\\n\\n# Example usage\\nquery = \"What are the benefits of using RAG?\"\\nresponse = rag_chain.invoke({\"question\": query})\\nprint(response)')"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["from langchain_core.prompts import ChatPromptTemplate\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel, Field\n", "\n", "### OpenAI\n", "\n", "# Grader prompt\n", "code_gen_prompt = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"You are a coding assistant with expertise in LCEL, LangChain expression language. \\n \n", "    Here is a full set of LCEL documentation:  \\n ------- \\n  {context} \\n ------- \\n Answer the user \n", "    question based on the above provided documentation. Ensure any code you provide can be executed \\n \n", "    with all required imports and variables defined. Structure your answer with a description of the code solution. \\n\n", "    Then list the imports. And finally list the functioning code block. Here is the user question:\"\"\",\n", "        ),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "\n", "# Data model\n", "class code(BaseModel):\n", "    \"\"\"Schema for code solutions to questions about LCEL.\"\"\"\n", "\n", "    prefix: str = Field(description=\"Description of the problem and approach\")\n", "    imports: str = Field(description=\"Code block import statements\")\n", "    code: str = Field(description=\"Code block not including import statements\")\n", "\n", "\n", "expt_llm = \"gpt-4o-mini\"\n", "llm = ChatOpenAI(temperature=0, model=expt_llm)\n", "code_gen_chain_oai = code_gen_prompt | llm.with_structured_output(code)\n", "question = \"How do I build a RAG chain in LCEL?\"\n", "solution = code_gen_chain_oai.invoke(\n", "    {\"context\": concatenated_content, \"messages\": [(\"user\", question)]}\n", ")\n", "solution"]}, {"cell_type": "code", "execution_count": 6, "id": "cd30b67d-96db-4e51-a540-ae23fcc1f878", "metadata": {}, "outputs": [], "source": ["from langchain_anthropic import ChatAnthropic\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "### Anthropic\n", "\n", "# Prompt to enforce tool use\n", "code_gen_prompt_claude = ChatPromptTemplate.from_messages(\n", "    [\n", "        (\n", "            \"system\",\n", "            \"\"\"<instructions> You are a coding assistant with expertise in LCEL, LangChain expression language. \\n \n", "    Here is the LCEL documentation:  \\n ------- \\n  {context} \\n ------- \\n Answer the user  question based on the \\n \n", "    above provided documentation. Ensure any code you provide can be executed with all required imports and variables \\n\n", "    defined. Structure your answer: 1) a prefix describing the code solution, 2) the imports, 3) the functioning code block. \\n\n", "    Invoke the code tool to structure the output correctly. </instructions> \\n Here is the user question:\"\"\",\n", "        ),\n", "        (\"placeholder\", \"{messages}\"),\n", "    ]\n", ")\n", "\n", "\n", "# LLM\n", "expt_llm = \"claude-3-opus-20240229\"\n", "llm = ChatAnthropic(\n", "    model=expt_llm,\n", "    default_headers={\"anthropic-beta\": \"tools-2024-04-04\"},\n", ")\n", "\n", "structured_llm_claude = llm.with_structured_output(code, include_raw=True)\n", "\n", "\n", "# Optional: Check for errors in case tool use is flaky\n", "def check_claude_output(tool_output):\n", "    \"\"\"Check for parse error or failure to call the tool\"\"\"\n", "\n", "    # Error with parsing\n", "    if tool_output[\"parsing_error\"]:\n", "        # Report back output and parsing errors\n", "        print(\"Parsing error!\")\n", "        raw_output = str(tool_output[\"raw\"].content)\n", "        error = tool_output[\"parsing_error\"]\n", "        raise ValueError(\n", "            f\"Error parsing your output! Be sure to invoke the tool. Output: {raw_output}. \\n Parse error: {error}\"\n", "        )\n", "\n", "    # Too<PERSON> was not invoked\n", "    elif not tool_output[\"parsed\"]:\n", "        print(\"Failed to invoke tool!\")\n", "        raise ValueError(\n", "            \"You did not use the provided tool! Be sure to invoke the tool to structure the output.\"\n", "        )\n", "    return tool_output\n", "\n", "\n", "# Chain with output check\n", "code_chain_claude_raw = (\n", "    code_gen_prompt_claude | structured_llm_claude | check_claude_output\n", ")\n", "\n", "\n", "def insert_errors(inputs):\n", "    \"\"\"Insert errors for tool parsing in the messages\"\"\"\n", "\n", "    # Get errors\n", "    error = inputs[\"error\"]\n", "    messages = inputs[\"messages\"]\n", "    messages += [\n", "        (\n", "            \"assistant\",\n", "            f\"Retry. You are required to fix the parsing errors: {error} \\n\\n You must invoke the provided tool.\",\n", "        )\n", "    ]\n", "    return {\n", "        \"messages\": messages,\n", "        \"context\": inputs[\"context\"],\n", "    }\n", "\n", "\n", "# This will be run as a fallback chain\n", "fallback_chain = insert_errors | code_chain_claude_raw\n", "N = 3  # <PERSON> re-tries\n", "code_gen_chain_re_try = code_chain_claude_raw.with_fallbacks(\n", "    fallbacks=[fallback_chain] * N, exception_key=\"error\"\n", ")\n", "\n", "\n", "def parse_output(solution):\n", "    \"\"\"When we add 'include_raw=True' to structured output,\n", "    it will return a dict w 'raw', 'parsed', 'parsing_error'.\"\"\"\n", "\n", "    return solution[\"parsed\"]\n", "\n", "\n", "# Optional: With re-try to correct for failure to invoke tool\n", "code_gen_chain = code_gen_chain_re_try | parse_output\n", "\n", "# No re-try\n", "code_gen_chain = code_gen_prompt_claude | structured_llm_claude | parse_output"]}, {"cell_type": "code", "execution_count": 7, "id": "9f14750f-dddc-485b-ba29-5392cdf4ba43", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["code(prefix=\"To build a RAG (Retrieval Augmented Generation) chain in LCEL, you can use a retriever to fetch relevant documents and then pass those documents to a chat model to generate a response based on the retrieved context. Here's an example of how to do this:\", imports='from langchain_expressions import retrieve, chat_completion', code='question = \"What is the capital of France?\"\\n\\nrelevant_docs = retrieve(question)\\n\\nresult = chat_completion(\\n    model=\\'openai-gpt35\\', \\n    messages=[\\n        {{{\"role\": \"system\", \"content\": \"Answer the question based on the retrieved context.}}},\\n        {{{\"role\": \"user\", \"content\": \\'\\'\\'\\n            Context: {relevant_docs}\\n            Question: {question}\\n        \\'\\'\\'}}\\n    ]\\n)\\n\\nprint(result)')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# Test\n", "question = \"How do I build a RAG chain in LCEL?\"\n", "solution = code_gen_chain.invoke(\n", "    {\"context\": concatenated_content, \"messages\": [(\"user\", question)]}\n", ")\n", "solution"]}, {"cell_type": "markdown", "id": "131f2055-2f64-4d19-a3d1-2d3cb8b42894", "metadata": {}, "source": ["## State \n", "\n", "Our state is a dict that will contain keys (errors, question, code generation) relevant to code generation."]}, {"cell_type": "code", "execution_count": 8, "id": "c185f1a2-e943-4bed-b833-4243c9c64092", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "from typing_extensions import TypedDict\n", "\n", "\n", "class GraphState(TypedDict):\n", "    \"\"\"\n", "    Represents the state of our graph.\n", "\n", "    Attributes:\n", "        error : Binary flag for control flow to indicate whether test error was tripped\n", "        messages : With user question, error messages, reasoning\n", "        generation : Code solution\n", "        iterations : Number of tries\n", "    \"\"\"\n", "\n", "    error: str\n", "    messages: List\n", "    generation: str\n", "    iterations: int"]}, {"cell_type": "markdown", "id": "64454465-26a3-40de-ad85-bcf59a2c3086", "metadata": {}, "source": ["## Graph \n", "\n", "Our graph lays out the logical flow shown in the figure above."]}, {"cell_type": "code", "execution_count": 9, "id": "b70e8301-63ae-4f7e-ad8f-c9a052fe3566", "metadata": {}, "outputs": [], "source": ["### Parameter\n", "\n", "# Max tries\n", "max_iterations = 3\n", "# Reflect\n", "# flag = 'reflect'\n", "flag = \"do not reflect\"\n", "\n", "### Nodes\n", "\n", "\n", "def generate(state: GraphState):\n", "    \"\"\"\n", "    Generate a code solution\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation\n", "    \"\"\"\n", "\n", "    print(\"---GENERATING CODE SOLUTION---\")\n", "\n", "    # State\n", "    messages = state[\"messages\"]\n", "    iterations = state[\"iterations\"]\n", "    error = state[\"error\"]\n", "\n", "    # We have been routed back to generation with an error\n", "    if error == \"yes\":\n", "        messages += [\n", "            (\n", "                \"user\",\n", "                \"Now, try again. Invoke the code tool to structure the output with a prefix, imports, and code block:\",\n", "            )\n", "        ]\n", "\n", "    # Solution\n", "    code_solution = code_gen_chain.invoke(\n", "        {\"context\": concatenated_content, \"messages\": messages}\n", "    )\n", "    messages += [\n", "        (\n", "            \"assistant\",\n", "            f\"{code_solution.prefix} \\n Imports: {code_solution.imports} \\n Code: {code_solution.code}\",\n", "        )\n", "    ]\n", "\n", "    # Increment\n", "    iterations = iterations + 1\n", "    return {\"generation\": code_solution, \"messages\": messages, \"iterations\": iterations}\n", "\n", "\n", "def code_check(state: GraphState):\n", "    \"\"\"\n", "    Check code\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, error\n", "    \"\"\"\n", "\n", "    print(\"---CHECKING CODE---\")\n", "\n", "    # State\n", "    messages = state[\"messages\"]\n", "    code_solution = state[\"generation\"]\n", "    iterations = state[\"iterations\"]\n", "\n", "    # Get solution components\n", "    imports = code_solution.imports\n", "    code = code_solution.code\n", "\n", "    # Check imports\n", "    try:\n", "        exec(imports)\n", "    except Exception as e:\n", "        print(\"---CODE IMPORT CHECK: FAILED---\")\n", "        error_message = [(\"user\", f\"Your solution failed the import test: {e}\")]\n", "        messages += error_message\n", "        return {\n", "            \"generation\": code_solution,\n", "            \"messages\": messages,\n", "            \"iterations\": iterations,\n", "            \"error\": \"yes\",\n", "        }\n", "\n", "    # Check execution\n", "    try:\n", "        exec(imports + \"\\n\" + code)\n", "    except Exception as e:\n", "        print(\"---CODE BLOCK CHECK: FAILED---\")\n", "        error_message = [(\"user\", f\"Your solution failed the code execution test: {e}\")]\n", "        messages += error_message\n", "        return {\n", "            \"generation\": code_solution,\n", "            \"messages\": messages,\n", "            \"iterations\": iterations,\n", "            \"error\": \"yes\",\n", "        }\n", "\n", "    # No errors\n", "    print(\"---NO CODE TEST FAILURES---\")\n", "    return {\n", "        \"generation\": code_solution,\n", "        \"messages\": messages,\n", "        \"iterations\": iterations,\n", "        \"error\": \"no\",\n", "    }\n", "\n", "\n", "def reflect(state: GraphState):\n", "    \"\"\"\n", "    Reflect on errors\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        state (dict): New key added to state, generation\n", "    \"\"\"\n", "\n", "    print(\"---GENERATING CODE SOLUTION---\")\n", "\n", "    # State\n", "    messages = state[\"messages\"]\n", "    iterations = state[\"iterations\"]\n", "    code_solution = state[\"generation\"]\n", "\n", "    # Prompt reflection\n", "\n", "    # Add reflection\n", "    reflections = code_gen_chain.invoke(\n", "        {\"context\": concatenated_content, \"messages\": messages}\n", "    )\n", "    messages += [(\"assistant\", f\"Here are reflections on the error: {reflections}\")]\n", "    return {\"generation\": code_solution, \"messages\": messages, \"iterations\": iterations}\n", "\n", "\n", "### Edges\n", "\n", "\n", "def decide_to_finish(state: GraphState):\n", "    \"\"\"\n", "    Determines whether to finish.\n", "\n", "    Args:\n", "        state (dict): The current graph state\n", "\n", "    Returns:\n", "        str: Next node to call\n", "    \"\"\"\n", "    error = state[\"error\"]\n", "    iterations = state[\"iterations\"]\n", "\n", "    if error == \"no\" or iterations == max_iterations:\n", "        print(\"---DECISION: FINISH---\")\n", "        return \"end\"\n", "    else:\n", "        print(\"---DECISION: RE-TRY SOLUTION---\")\n", "        if flag == \"reflect\":\n", "            return \"reflect\"\n", "        else:\n", "            return \"generate\""]}, {"cell_type": "code", "execution_count": 10, "id": "f66b4e00-4731-42c8-bc38-72dd0ff7c92c", "metadata": {}, "outputs": [], "source": ["from langgraph.graph import END, StateGraph, START\n", "\n", "workflow = StateGraph(GraphState)\n", "\n", "# Define the nodes\n", "workflow.add_node(\"generate\", generate)  # generation solution\n", "workflow.add_node(\"check_code\", code_check)  # check code\n", "workflow.add_node(\"reflect\", reflect)  # reflect\n", "\n", "# Build graph\n", "workflow.add_edge(START, \"generate\")\n", "workflow.add_edge(\"generate\", \"check_code\")\n", "workflow.add_conditional_edges(\n", "    \"check_code\",\n", "    decide_to_finish,\n", "    {\n", "        \"end\": END,\n", "        \"reflect\": \"reflect\",\n", "        \"generate\": \"generate\",\n", "    },\n", ")\n", "workflow.add_edge(\"reflect\", \"generate\")\n", "app = workflow.compile()"]}, {"cell_type": "code", "execution_count": 13, "id": "9bcaafe4-ddcf-4fab-8620-2d9b6c508f98", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---GENERATING CODE SOLUTION---\n", "---CHECKING CODE---\n", "---CODE IMPORT CHECK: FAILED---\n", "---DECISION: RE-TRY SOLUTION---\n", "---GENERATING CODE SOLUTION---\n", "---CHECKING CODE---\n", "---CODE IMPORT CHECK: FAILED---\n", "---DECISION: RE-TRY SOLUTION---\n", "---GENERATING CODE SOLUTION---\n", "---CHECKING CODE---\n", "---CODE BLOCK CHECK: FAILED---\n", "---DECISION: FINISH---\n"]}], "source": ["question = \"How can I directly pass a string to a runnable and use it to construct the input needed for my prompt?\"\n", "solution = app.invoke({\"messages\": [(\"user\", question)], \"iterations\": 0, \"error\": \"\"})"]}, {"cell_type": "code", "execution_count": 18, "id": "9d28692e", "metadata": {}, "outputs": [{"data": {"text/plain": ["code(prefix='To directly pass a string to a runnable and use it to construct the input needed for a prompt, you can use the `_from_value` method on a PromptTemplate in LCEL. Create a PromptTemplate with the desired template string, then call `_from_value` on it with a dictionary mapping the input variable names to their values. This will return a PromptValue that you can pass directly to any chain or model that accepts a prompt input.', imports='from langchain_core.prompts import PromptTemplate', code='user_string = \"langchain is awesome\"\\n\\nprompt_template = PromptTemplate.from_template(\"Tell me more about how {user_input}.\")\\n\\nprompt_value = prompt_template._from_value({\"user_input\": user_string})\\n\\n# Pass the PromptValue directly to a model or chain \\nchain.run(prompt_value)')"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["solution[\"generation\"]"]}, {"cell_type": "markdown", "id": "744f48a5-9ad3-4342-899f-7dd4266a9a15", "metadata": {}, "source": ["## <PERSON>l"]}, {"cell_type": "markdown", "id": "89852874-b538-4c8d-a4c3-1d68302db492", "metadata": {}, "source": ["[Here](https://smith.langchain.com/public/326674a6-62bd-462d-88ae-eea49d503f9d/d) is a public dataset of LCEL questions. \n", "\n", "I saved this as `lcel-teacher-eval`.\n", "\n", "You can also find the csv [here](https://github.com/langchain-ai/lcel-teacher/blob/main/eval/eval.csv)."]}, {"cell_type": "code", "execution_count": 19, "id": "678e8954-56b5-4cc6-be26-f7f2a060b242", "metadata": {}, "outputs": [], "source": ["import langsmith\n", "\n", "client = langsmith.Client()"]}, {"cell_type": "code", "execution_count": 20, "id": "ef7cf662-7a6f-4dee-965c-6309d4045feb", "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset(name='lcel-teacher-eval', description='Eval set for LCEL teacher', data_type=<DataType.kv: 'kv'>, id=UUID('8b57696d-14ea-4f00-9997-b3fc74a16846'), created_at=datetime.datetime(2024, 9, 16, 22, 50, 4, 169288, tzinfo=datetime.timezone.utc), modified_at=datetime.datetime(2024, 9, 16, 22, 50, 4, 169288, tzinfo=datetime.timezone.utc), example_count=0, session_count=0, last_session_start_time=None, inputs_schema=None, outputs_schema=None)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# Clone the dataset to your tenant to use it\n", "try:\n", "    public_dataset = (\n", "        \"https://smith.langchain.com/public/326674a6-62bd-462d-88ae-eea49d503f9d/d\"\n", "    )\n", "    client.clone_public_dataset(public_dataset)\n", "except:\n", "    print(\"Please setup LangSmith\")"]}, {"cell_type": "markdown", "id": "9d171396-022b-47ec-a741-c782aff9fdae", "metadata": {}, "source": ["Custom evals."]}, {"cell_type": "code", "execution_count": 21, "id": "455a34ea-52cb-4ae5-9f4a-7e4a08cd0c09", "metadata": {}, "outputs": [], "source": ["from langsmith.schemas import Example, Run\n", "\n", "\n", "def check_import(run: Run, example: Example) -> dict:\n", "    imports = run.outputs.get(\"imports\")\n", "    try:\n", "        exec(imports)\n", "        return {\"key\": \"import_check\", \"score\": 1}\n", "    except Exception:\n", "        return {\"key\": \"import_check\", \"score\": 0}\n", "\n", "\n", "def check_execution(run: Run, example: Example) -> dict:\n", "    imports = run.outputs.get(\"imports\")\n", "    code = run.outputs.get(\"code\")\n", "    try:\n", "        exec(imports + \"\\n\" + code)\n", "        return {\"key\": \"code_execution_check\", \"score\": 1}\n", "    except Exception:\n", "        return {\"key\": \"code_execution_check\", \"score\": 0}"]}, {"cell_type": "markdown", "id": "c90bf261-0d94-4779-bbde-c76adeefe3d7", "metadata": {}, "source": ["Compare LangGraph to Context Stuffing."]}, {"cell_type": "code", "execution_count": 33, "id": "c8fa6bcb-b245-4422-b79a-582cd8a7d7ea", "metadata": {}, "outputs": [], "source": ["def predict_base_case(example: dict):\n", "    \"\"\"Context stuffing\"\"\"\n", "    solution = code_gen_chain.invoke(\n", "        {\"context\": concatenated_content, \"messages\": [(\"user\", example[\"question\"])]}\n", "    )\n", "    return {\"imports\": solution.imports, \"code\": solution.code}\n", "\n", "\n", "def predict_langgraph(example: dict):\n", "    \"\"\"LangGraph\"\"\"\n", "    graph = app.invoke(\n", "        {\"messages\": [(\"user\", example[\"question\"])], \"iterations\": 0, \"error\": \"\"}\n", "    )\n", "    solution = graph[\"generation\"]\n", "    return {\"imports\": solution.imports, \"code\": solution.code}"]}, {"cell_type": "code", "execution_count": 34, "id": "d9c57468-97f6-47d6-a5e9-c09b53bfdd83", "metadata": {}, "outputs": [], "source": ["from langsmith.evaluation import evaluate\n", "\n", "# Evaluator\n", "code_evalulator = [check_import, check_execution]\n", "\n", "# Dataset\n", "dataset_name = \"lcel-teacher-eval\""]}, {"cell_type": "code", "execution_count": null, "id": "2dacccf0-d73f-4017-aaf0-9806ffe5bd2c", "metadata": {}, "outputs": [], "source": ["# Run base case\n", "try:\n", "    experiment_results_ = evaluate(\n", "        predict_base_case,\n", "        data=dataset_name,\n", "        evaluators=code_evalulator,\n", "        experiment_prefix=f\"test-without-langgraph-{expt_llm}\",\n", "        max_concurrency=2,\n", "        metadata={\n", "            \"llm\": expt_llm,\n", "        },\n", "    )\n", "except:\n", "    print(\"Please setup LangSmith\")"]}, {"cell_type": "code", "execution_count": null, "id": "71d90f9e-9dad-410c-a709-093d275029ae", "metadata": {}, "outputs": [], "source": ["# Run with langgraph\n", "try:\n", "    experiment_results = evaluate(\n", "        predict_langgraph,\n", "        data=dataset_name,\n", "        evaluators=code_evalulator,\n", "        experiment_prefix=f\"test-with-langgraph-{expt_llm}-{flag}\",\n", "        max_concurrency=2,\n", "        metadata={\n", "            \"llm\": expt_llm,\n", "            \"feedback\": flag,\n", "        },\n", "    )\n", "except:\n", "    print(\"Please setup LangSmith\")"]}, {"cell_type": "markdown", "id": "d69da747-b4ea-455d-9314-60c3d9d30549", "metadata": {}, "source": ["`Results:`\n", "\n", "* `LangGraph outperforms base case`: adding re-try loop improve performance\n", "* `Reflection did not help`: reflection prior to re-try regression vs just passing errors directly back to the LLM\n", "* `GPT-4 outperforms Claude<PERSON>`: <PERSON><PERSON> had 3 and 1 run fail due to tool-use error for Opus and Haiku, respectively\n", "\n", "https://smith.langchain.com/public/78a3d858-c811-4e46-91cb-0f10ef56260b/d"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}