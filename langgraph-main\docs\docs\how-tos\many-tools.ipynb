{"cells": [{"cell_type": "markdown", "id": "5fa317ef-b9a7-4432-ba85-ce71b8dfbdc6", "metadata": {}, "source": ["# How to handle large numbers of tools\n", "\n", "<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Prerequisites</p>\n", "    <p>\n", "        This guide assumes familiarity with the following:\n", "        <ul>\n", "            <li>\n", "                <a href=\"https://python.langchain.com/docs/concepts/#tools\">\n", "                    Tools\n", "                </a>\n", "            </li>\n", "            <li>\n", "                <a href=\"https://python.langchain.com/docs/concepts/#chat-models/\">\n", "                    Chat Models\n", "                </a>\n", "            </li>\n", "            <li>\n", "                <a href=\"https://python.langchain.com/docs/concepts/#embedding-models\">\n", "                    Embedding Models\n", "                </a>\n", "            </li>\n", "            <li>\n", "                <a href=\"https://python.langchain.com/docs/concepts/#vector-stores\">\n", "                    Vectorstores\n", "                </a>\n", "            </li>   \n", "            <li>\n", "                <a href=\"https://python.langchain.com/docs/concepts/#documents\">\n", "                    Document\n", "                </a>\n", "            </li>\n", "        </ul>\n", "    </p>\n", "</div> \n", "\n", "\n", "The subset of available tools to call is generally at the discretion of the model (although many providers also enable the user to [specify or constrain the choice of tool](https://python.langchain.com/docs/how_to/tool_choice/)). As the number of available tools grows, you may want to limit the scope of the LLM's selection, to decrease token consumption and to help manage sources of error in LLM reasoning.\n", "\n", "Here we will demonstrate how to dynamically adjust the tools available to a model. Bottom line up front: like [RAG](https://python.langchain.com/docs/concepts/#retrieval) and similar methods, we prefix the model invocation by retrieving over available tools. Although we demonstrate one implementation that searches over tool descriptions, the details of the tool selection can be customized as needed.\n", "\n", "## Setup\n", "\n", "First, let's install the required packages and set our API keys"]}, {"cell_type": "code", "execution_count": 1, "id": "9b6c62bd", "metadata": {}, "outputs": [], "source": ["%%capture --no-stderr\n", "%pip install --quiet -U langgraph langchain_openai numpy"]}, {"cell_type": "code", "execution_count": null, "id": "360d7ff6", "metadata": {}, "outputs": [], "source": ["import getpass\n", "import os\n", "\n", "\n", "def _set_env(var: str):\n", "    if not os.environ.get(var):\n", "        os.environ[var] = getpass.getpass(f\"{var}: \")\n", "\n", "\n", "_set_env(\"OPENAI_API_KEY\")"]}, {"cell_type": "markdown", "id": "25f9f6a0", "metadata": {}, "source": ["<div class=\"admonition tip\">\n", "    <p class=\"admonition-title\">Set up <a href=\"https://smith.langchain.com\">LangSmith</a> for LangGraph development</p>\n", "    <p style=\"padding-top: 5px;\">\n", "        Sign up for LangSmith to quickly spot issues and improve the performance of your LangGraph projects. LangSmith lets you use trace data to debug, test, and monitor your LLM apps built with LangGraph — read more about how to get started <a href=\"https://docs.smith.langchain.com\">here</a>. \n", "    </p>\n", "</div>"]}, {"cell_type": "markdown", "id": "1a417013-ddc4-463b-8ea0-0904bd232827", "metadata": {}, "source": ["## Define the tools"]}, {"cell_type": "markdown", "id": "24708f3b-18b1-4b42-9f6a-0d4827222918", "metadata": {}, "source": ["Let's consider a toy example in which we have one tool for each publicly traded company in the [S&P 500 index](https://en.wikipedia.org/wiki/S%26P_500). Each tool fetches company-specific information based on the year provided as a parameter.\n", "\n", "We first construct a registry that associates a unique identifier with a schema for each tool. We will represent the tools using JSON schema, which can be bound directly to chat models supporting tool calling."]}, {"cell_type": "code", "execution_count": 3, "id": "da30c3f1-127f-4828-8609-94e16719f0be", "metadata": {}, "outputs": [], "source": ["import re\n", "import uuid\n", "\n", "from langchain_core.tools import StructuredTool\n", "\n", "\n", "def create_tool(company: str) -> dict:\n", "    \"\"\"Create schema for a placeholder tool.\"\"\"\n", "    # Remove non-alphanumeric characters and replace spaces with underscores for the tool name\n", "    formatted_company = re.sub(r\"[^\\w\\s]\", \"\", company).replace(\" \", \"_\")\n", "\n", "    def company_tool(year: int) -> str:\n", "        # Placeholder function returning static revenue information for the company and year\n", "        return f\"{company} had revenues of $100 in {year}.\"\n", "\n", "    return StructuredTool.from_function(\n", "        company_tool,\n", "        name=formatted_company,\n", "        description=f\"Information about {company}\",\n", "    )\n", "\n", "\n", "# Abbreviated list of S&P 500 companies for demonstration\n", "s_and_p_500_companies = [\n", "    \"3M\",\n", "    \"<PERSON><PERSON><PERSON><PERSON>\",\n", "    \"Abbott\",\n", "    \"Accenture\",\n", "    \"Advanced Micro Devices\",\n", "    \"Yum! Brands\",\n", "    \"Zebra Technologies\",\n", "    \"Zimmer Biomet\",\n", "    \"Zoetis\",\n", "]\n", "\n", "# Create a tool for each company and store it in a registry with a unique UUID as the key\n", "tool_registry = {\n", "    str(uuid.uuid4()): create_tool(company) for company in s_and_p_500_companies\n", "}"]}, {"cell_type": "markdown", "id": "ba17b047-73ed-4385-adc2-f02012db2206", "metadata": {}, "source": ["## Define the graph"]}, {"cell_type": "markdown", "id": "2055548d-3d14-4aaf-9588-abf70f28b5d6", "metadata": {}, "source": ["### Tool selection"]}, {"cell_type": "markdown", "id": "8798a0d2-ea93-45bc-ab55-071ab975f2c2", "metadata": {}, "source": ["We will construct a node that retrieves a subset of available tools given the information in the state-- such as a recent user message. In general, the full scope of [retrieval solutions](https://python.langchain.com/docs/concepts/#retrieval) are available for this step. As a simple solution, we index embeddings of tool descriptions in a vector store, and associate user queries to tools via semantic search."]}, {"cell_type": "code", "execution_count": 4, "id": "435b0201-7296-4617-abf8-2c757a71f6b5", "metadata": {}, "outputs": [], "source": ["from langchain_core.documents import Document\n", "from langchain_core.vectorstores import InMemoryVectorStore\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "tool_documents = [\n", "    Document(\n", "        page_content=tool.description,\n", "        id=id,\n", "        metadata={\"tool_name\": tool.name},\n", "    )\n", "    for id, tool in tool_registry.items()\n", "]\n", "\n", "vector_store = InMemoryVectorStore(embedding=OpenAIEmbeddings())\n", "document_ids = vector_store.add_documents(tool_documents)"]}, {"cell_type": "markdown", "id": "e9ce366b-b5e7-41e9-b4a9-d775b9be0d09", "metadata": {}, "source": ["### Incorporating with an agent\n", "\n", "We will use a typical React agent graph (e.g., as used in the [quickstart](https://langchain-ai.github.io/langgraph/tutorials/introduction/#part-2-enhancing-the-chatbot-with-tools)), with some modifications:\n", "\n", "- We add a `selected_tools` key to the state, which stores our selected subset of tools;\n", "- We set the entry point of the graph to be a `select_tools` node, which populates this element of the state;\n", "- We bind the selected subset of tools to the chat model within the `agent` node."]}, {"cell_type": "code", "execution_count": 6, "id": "d319fea9-e8ae-4763-a785-b2bf72239ae4", "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "\n", "from langchain_openai import ChatOpenAI\n", "from typing_extensions import TypedDict\n", "\n", "from langgraph.graph import StateGraph, START\n", "from langgraph.graph.message import add_messages\n", "from langgraph.prebuilt import ToolNode, tools_condition\n", "\n", "\n", "# Define the state structure using TypedDict.\n", "# It includes a list of messages (processed by add_messages)\n", "# and a list of selected tool IDs.\n", "class State(TypedDict):\n", "    messages: Annotated[list, add_messages]\n", "    selected_tools: list[str]\n", "\n", "\n", "builder = StateGraph(State)\n", "\n", "# Retrieve all available tools from the tool registry.\n", "tools = list(tool_registry.values())\n", "llm = ChatOpenAI()\n", "\n", "\n", "# The agent function processes the current state\n", "# by binding selected tools to the LLM.\n", "def agent(state: State):\n", "    # Map tool IDs to actual tools\n", "    # based on the state's selected_tools list.\n", "    selected_tools = [tool_registry[id] for id in state[\"selected_tools\"]]\n", "    # Bind the selected tools to the LLM for the current interaction.\n", "    llm_with_tools = llm.bind_tools(selected_tools)\n", "    # Invoke the LLM with the current messages and return the updated message list.\n", "    return {\"messages\": [llm_with_tools.invoke(state[\"messages\"])]}\n", "\n", "\n", "# The select_tools function selects tools based on the user's last message content.\n", "def select_tools(state: State):\n", "    last_user_message = state[\"messages\"][-1]\n", "    query = last_user_message.content\n", "    tool_documents = vector_store.similarity_search(query)\n", "    return {\"selected_tools\": [document.id for document in tool_documents]}\n", "\n", "\n", "builder.add_node(\"agent\", agent)\n", "builder.add_node(\"select_tools\", select_tools)\n", "\n", "tool_node = ToolNode(tools=tools)\n", "builder.add_node(\"tools\", tool_node)\n", "\n", "builder.add_conditional_edges(\"agent\", tools_condition, path_map=[\"tools\", \"__end__\"])\n", "builder.add_edge(\"tools\", \"agent\")\n", "builder.add_edge(\"select_tools\", \"agent\")\n", "builder.add_edge(START, \"select_tools\")\n", "graph = builder.compile()"]}, {"cell_type": "code", "execution_count": 7, "id": "35cab3b2-4d03-4cb5-ba10-f7d3a5ad5244", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "code", "execution_count": 14, "id": "66f62a69-989b-46ce-80b3-97a867e36782", "metadata": {}, "outputs": [], "source": ["user_input = \"Can you give me some information about AMD in 2022?\"\n", "\n", "result = graph.invoke({\"messages\": [(\"user\", user_input)]})"]}, {"cell_type": "code", "execution_count": 15, "id": "479a459d-6896-4960-aae9-9f1259fb47d1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ab9c0d59-3d16-448d-910c-73cf10a26020', 'f5eff8f6-7fb9-47b6-b54f-19872a52db84', '2962e168-9ef4-48dc-8b7c-9227e7956d39', '24a9fb82-19fe-4a88-944e-47bc4032e94a']\n"]}], "source": ["print(result[\"selected_tools\"])"]}, {"cell_type": "code", "execution_count": 16, "id": "376f28fd-3f7f-4ae5-a34c-baef1778e82b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Can you give me some information about AMD in 2022?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Advanced_Micro_Devices (call_CRxQ0oT7NY7lqf35DaRNTJ35)\n", " Call ID: call_CRxQ0oT7NY7lqf35DaRNTJ35\n", "  Args:\n", "    year: 2022\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: Advanced_Micro_Devices\n", "\n", "Advanced Micro Devices had revenues of $100 in 2022.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "In 2022, Advanced Micro Devices (AMD) had revenues of $100.\n"]}], "source": ["for message in result[\"messages\"]:\n", "    message.pretty_print()"]}, {"cell_type": "markdown", "id": "3bd847ef-4627-4fc2-99f9-c2b17cf83f95", "metadata": {}, "source": ["## Repeating tool selection\n", "\n", "To manage errors from incorrect tool selection, we could revisit the `select_tools` node. One option for implementing this is to modify `select_tools` to generate the vector store query using all messages in the state (e.g., with a chat model) and add an edge routing from `tools` to `select_tools`.\n", "\n", "We implement this change below. For demonstration purposes, we simulate an error in the initial tool selection by adding a `hack_remove_tool_condition` to the `select_tools` node, which removes the correct tool on the first iteration of the node. Note that on the second iteration, the agent finishes the run as it has access to the correct tool."]}, {"cell_type": "markdown", "id": "985a5388", "metadata": {}, "source": ["<div class=\"admonition note\">\n", "    <p class=\"admonition-title\">Using Pydantic with <PERSON><PERSON><PERSON><PERSON></p>\n", "    <p>\n", "        This notebook uses Pydantic v2 <code>BaseModel</code>, which requires <code>langchain-core >= 0.3</code>. Using <code>langchain-core < 0.3</code> will result in errors due to mixing of Pydantic v1 and v2 <code>BaseModels</code>.\n", "    </p>\n", "</div>  "]}, {"cell_type": "code", "execution_count": null, "id": "1954a5f1-91e4-4b32-9be9-c8bc1cc43cb5", "metadata": {}, "outputs": [], "source": ["from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage\n", "from langgraph.pregel.retry import RetryPolicy\n", "\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class QueryForTools(BaseModel):\n", "    \"\"\"Generate a query for additional tools.\"\"\"\n", "\n", "    query: str = Field(..., description=\"Query for additional tools.\")\n", "\n", "\n", "def select_tools(state: State):\n", "    \"\"\"Selects tools based on the last message in the conversation state.\n", "\n", "    If the last message is from a human, directly uses the content of the message\n", "    as the query. Otherwise, constructs a query using a system message and invokes\n", "    the LLM to generate tool suggestions.\n", "    \"\"\"\n", "    last_message = state[\"messages\"][-1]\n", "    hack_remove_tool_condition = False  # Simulate an error in the first tool selection\n", "\n", "    if isinstance(last_message, HumanMessage):\n", "        query = last_message.content\n", "        hack_remove_tool_condition = True  # Simulate wrong tool selection\n", "    else:\n", "        assert isinstance(last_message, ToolMessage)\n", "        system = SystemMessage(\n", "            \"Given this conversation, generate a query for additional tools. \"\n", "            \"The query should be a short string containing what type of information \"\n", "            \"is needed. If no further information is needed, \"\n", "            \"set more_information_needed False and populate a blank string for the query.\"\n", "        )\n", "        input_messages = [system] + state[\"messages\"]\n", "        response = llm.bind_tools([QueryForTools], tool_choice=True).invoke(\n", "            input_messages\n", "        )\n", "        query = response.tool_calls[0][\"args\"][\"query\"]\n", "\n", "    # Search the tool vector store using the generated query\n", "    tool_documents = vector_store.similarity_search(query)\n", "    if hack_remove_tool_condition:\n", "        # Simulate error by removing the correct tool from the selection\n", "        selected_tools = [\n", "            document.id\n", "            for document in tool_documents\n", "            if document.metadata[\"tool_name\"] != \"Advanced_Micro_Devices\"\n", "        ]\n", "    else:\n", "        selected_tools = [document.id for document in tool_documents]\n", "    return {\"selected_tools\": selected_tools}\n", "\n", "\n", "graph_builder = StateGraph(State)\n", "graph_builder.add_node(\"agent\", agent)\n", "graph_builder.add_node(\n", "    \"select_tools\", select_tools, retry_policy=RetryPolicy(max_attempts=3)\n", ")\n", "\n", "tool_node = ToolNode(tools=tools)\n", "graph_builder.add_node(\"tools\", tool_node)\n", "\n", "graph_builder.add_conditional_edges(\n", "    \"agent\",\n", "    tools_condition,\n", ")\n", "graph_builder.add_edge(\"tools\", \"select_tools\")\n", "graph_builder.add_edge(\"select_tools\", \"agent\")\n", "graph_builder.add_edge(START, \"select_tools\")\n", "graph = graph_builder.compile()"]}, {"cell_type": "code", "execution_count": 47, "id": "9110789a-843a-4c21-aeff-8841b24f7674", "metadata": {}, "outputs": [{"data": {"image/jpeg": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Image, display\n", "\n", "try:\n", "    display(Image(graph.get_graph().draw_mermaid_png()))\n", "except Exception:\n", "    # This requires some extra dependencies and is optional\n", "    pass"]}, {"cell_type": "code", "execution_count": 48, "id": "bee04c3d-0e36-4443-b0c8-10986a5f6e39", "metadata": {}, "outputs": [], "source": ["user_input = \"Can you give me some information about AMD in 2022?\"\n", "\n", "result = graph.invoke({\"messages\": [(\"user\", user_input)]})"]}, {"cell_type": "code", "execution_count": 49, "id": "6906fb50-435c-4473-bbb6-5353433b9199", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["================================\u001b[1m Human Message \u001b[0m=================================\n", "\n", "Can you give me some information about AMD in 2022?\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Accenture (call_qGmwFnENwwzHOYJXiCAaY5Mx)\n", " Call ID: call_qGmwFnENwwzHOYJXiCAaY5Mx\n", "  Args:\n", "    year: 2022\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: Accenture\n", "\n", "Accenture had revenues of $100 in 2022.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "Tool Calls:\n", "  Advanced_Micro_Devices (call_u9e5UIJtiieXVYi7Y9GgyDpn)\n", " Call ID: call_u9e5UIJtiieXVYi7Y9GgyDpn\n", "  Args:\n", "    year: 2022\n", "=================================\u001b[1m Tool Message \u001b[0m=================================\n", "Name: Advanced_Micro_Devices\n", "\n", "Advanced Micro Devices had revenues of $100 in 2022.\n", "==================================\u001b[1m Ai Message \u001b[0m==================================\n", "\n", "In 2022, AMD had revenues of $100.\n"]}], "source": ["for message in result[\"messages\"]:\n", "    message.pretty_print()"]}, {"cell_type": "markdown", "id": "177aedfa-cec5-45d0-82ad-efc0233aa6b4", "metadata": {}, "source": ["## Next steps\n", "\n", "This guide provides a minimal implementation for dynamically selecting tools. There is a host of possible improvements and optimizations:\n", "\n", "- **Repeating tool selection**: Here, we repeated tool selection by modifying the `select_tools` node. Another option is to equip the agent with a `reselect_tools` tool, allowing it to re-select tools at its discretion.\n", "- **Optimizing tool selection**: In general, the full scope of [retrieval solutions](https://python.langchain.com/docs/concepts/#retrieval) are available for tool selection. Additional options include:\n", "  - Group tools and retrieve over groups;\n", "  - Use a chat model to select tools or groups of tool."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}