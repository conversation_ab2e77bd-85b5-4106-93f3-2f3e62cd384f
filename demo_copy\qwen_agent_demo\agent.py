from qwen_agent.agents import Assistant
from qwen_agent.utils.output_beautify import typewriter_print
from tools import PlotTool # 导入tools

def init_agent_service():
  agent = Assistant(
    llm={
      'model': 'Qwen3-235B-A22B-BF16',
      'model_server': 'xxx',
      'api_key': 'xxx',
    }, 
    function_list=[
      {
        'mcpServers': {
          'sqlite': { # 更多现成的mcp server：https://mcp.so/
            'command': 'uvx',
            'args': [
              'mcp-server-sqlite',
              '--db-path',
              'test.db'
            ]
          },
          'firecrawl-mcp': {
            'command': 'npx',
            'args': ['-y', 'firecrawl-mcp'],
            'env': {
              'FIRECRAWL_API_KEY': 'fc-73a9808a328a462ab6d43f0b3f9cf5ed'
            }
          },
          'mcp_demo': { # 自定义 mcp 服务
            'command': '../mcp_demo/dist/server',
            'args': [],
            'cwd': '../mcp_demo'
          }
        }
      },
      'data_visualization',
    ],
    name='数据分析师',
    description='具备提取网页信息、数据分析、操作数据库、绘制统计图表的能力',
    system_message='你是一个数据分析师，可以提取网页信息进行数据分析'
  )

  return agent

def run_query(query=None):
  bot = init_agent_service()

  from qwen_agent.gui import WebUI

  chatbot_config = {
    'prompt.suggestions': [
      '帮我创建一个项目统计表,表名是qwenlm_projects,包含id, name, stars字段。https://github.com/orgs/QwenLM/repositories 提取这一页的Markdown 文档，然后将统计数据插入qwenlm_projects表，并绘制一张项目统计折线图。',
      # '绘制一张项目统计折线图，数据是 [10, 8, 100, 4]',
      # '9+10等于多少'
    ]
  }

  WebUI(
    bot,
    chatbot_config=chatbot_config
  ).run()

if __name__ == '__main__':
  run_query()