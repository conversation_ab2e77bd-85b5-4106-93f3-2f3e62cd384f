// 数学计算系统前端 JavaScript

// 全局变量
let currentThreadId = null;
let isCalculating = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用
async function initializeApp() {
    console.log('初始化数学计算系统...');
    
    // 生成会话ID
    currentThreadId = generateThreadId();
    
    // 加载系统状态
    await loadSystemStatus();
    
    // 加载可用工具
    await loadAvailableTools();
    
    // 加载计算示例
    await loadCalculationExamples();
    
    // 设置输入框焦点
    document.getElementById('question-input').focus();
    
    console.log('系统初始化完成');
}

// 生成会话ID
function generateThreadId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 加载系统状态
async function loadSystemStatus() {
    try {
        const response = await fetch('/health');
        const data = await response.json();
        
        const statusHtml = `
            <div class="status-item">
                <span class="status-label">系统状态</span>
                <span class="status-value status-healthy">
                    <i class="fas fa-check-circle me-1"></i>${data.status}
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">会话ID</span>
                <span class="status-value">
                    <small>${currentThreadId.substr(0, 12)}...</small>
                </span>
            </div>
            <div class="status-item">
                <span class="status-label">连接状态</span>
                <span class="status-value status-healthy">
                    <i class="fas fa-wifi me-1"></i>已连接
                </span>
            </div>
        `;
        
        document.getElementById('system-status').innerHTML = statusHtml;
    } catch (error) {
        console.error('加载系统状态失败:', error);
        document.getElementById('system-status').innerHTML = `
            <div class="status-item">
                <span class="status-label">系统状态</span>
                <span class="status-value status-error">
                    <i class="fas fa-exclamation-triangle me-1"></i>连接失败
                </span>
            </div>
        `;
    }
}

// 加载可用工具
async function loadAvailableTools() {
    try {
        const response = await fetch('/api/tools');
        const data = await response.json();
        
        let toolsHtml = '';
        data.tools.forEach(tool => {
            toolsHtml += `
                <div class="tool-item" onclick="insertToolExample('${tool.name}')">
                    <div class="tool-name">${tool.name}</div>
                    <div class="tool-description">${tool.description}</div>
                    <div class="tool-parameters">
                        参数: ${tool.parameters.join(', ')}
                    </div>
                </div>
            `;
        });
        
        document.getElementById('tools-list').innerHTML = toolsHtml;
    } catch (error) {
        console.error('加载工具列表失败:', error);
        document.getElementById('tools-list').innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <small class="d-block mt-2">加载失败</small>
            </div>
        `;
    }
}

// 加载计算示例
async function loadCalculationExamples() {
    try {
        const response = await fetch('/api/examples');
        const data = await response.json();
        
        let examplesHtml = '';
        data.examples.forEach(category => {
            examplesHtml += `
                <div class="example-category">
                    <div class="example-category-title">${category.category}</div>
            `;
            
            category.examples.forEach(example => {
                examplesHtml += `
                    <div class="example-item" onclick="insertExample('${example}')">
                        ${example}
                    </div>
                `;
            });
            
            examplesHtml += '</div>';
        });
        
        document.getElementById('examples-list').innerHTML = examplesHtml;
    } catch (error) {
        console.error('加载示例失败:', error);
        document.getElementById('examples-list').innerHTML = `
            <div class="text-center text-danger">
                <i class="fas fa-exclamation-triangle"></i>
                <small class="d-block mt-2">加载失败</small>
            </div>
        `;
    }
}

// 插入工具示例
function insertToolExample(toolName) {
    const examples = {
        'add': '计算 15 + 27',
        'subtract': '计算 100 - 45',
        'multiply': '计算 8 × 9',
        'divide': '计算 144 ÷ 12',
        'power': '计算 2 的 8 次方',
        'square_root': '求 144 的平方根',
        'percentage': '计算 25 占 200 的百分比',
        'factorial': '计算 5 的阶乘',
        'solve_linear_equation': '解方程 3x + 6 = 0',
        'calculate_expression': '计算 (2 + 3) × 4 - 1'
    };
    
    const example = examples[toolName] || `使用 ${toolName} 工具`;
    document.getElementById('question-input').value = example;
    document.getElementById('question-input').focus();
}

// 插入示例
function insertExample(example) {
    document.getElementById('question-input').value = example;
    document.getElementById('question-input').focus();
}

// 插入文本
function insertText(text) {
    const input = document.getElementById('question-input');
    const cursorPos = input.selectionStart;
    const textBefore = input.value.substring(0, cursorPos);
    const textAfter = input.value.substring(cursorPos);
    
    input.value = textBefore + text + textAfter;
    input.focus();
    input.setSelectionRange(cursorPos + text.length, cursorPos + text.length);
}

// 处理键盘事件
function handleKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendQuestion();
    }
}

// 发送问题
async function sendQuestion() {
    const input = document.getElementById('question-input');
    const question = input.value.trim();
    
    if (!question || isCalculating) {
        return;
    }
    
    // 清空输入框
    input.value = '';
    
    // 添加用户消息到聊天历史
    addMessage(question, 'user');
    
    // 显示加载消息
    const loadingId = addLoadingMessage();
    
    // 设置计算状态
    isCalculating = true;
    updateSendButton(true);
    
    try {
        // 发送计算请求
        const response = await fetch('/api/calculate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                question: question,
                thread_id: currentThreadId
            })
        });
        
        const data = await response.json();
        
        // 移除加载消息
        removeLoadingMessage(loadingId);
        
        if (data.success) {
            // 添加助手回复
            addMessage(data.result, 'assistant');
        } else {
            // 添加错误消息
            addMessage(`计算出错: ${data.error}`, 'error');
        }
        
    } catch (error) {
        console.error('发送请求失败:', error);
        removeLoadingMessage(loadingId);
        addMessage('网络连接错误，请检查服务器状态', 'error');
    } finally {
        // 重置计算状态
        isCalculating = false;
        updateSendButton(false);
        input.focus();
    }
}

// 添加消息到聊天历史
function addMessage(content, type) {
    const chatHistory = document.getElementById('chat-history');
    const messageDiv = document.createElement('div');
    
    let iconClass = '';
    let messageClass = '';
    
    switch (type) {
        case 'user':
            messageClass = 'user-message';
            iconClass = 'fas fa-user';
            break;
        case 'assistant':
            messageClass = 'assistant-message';
            iconClass = 'fas fa-robot';
            break;
        case 'error':
            messageClass = 'error-message';
            iconClass = 'fas fa-exclamation-triangle';
            break;
    }
    
    messageDiv.className = `message ${messageClass}`;
    messageDiv.innerHTML = `
        <div class="message-content">
            <i class="${iconClass} me-2"></i>
            ${content}
        </div>
        <div class="message-time">
            <small class="text-muted">${new Date().toLocaleTimeString()}</small>
        </div>
    `;
    
    chatHistory.appendChild(messageDiv);
    chatHistory.scrollTop = chatHistory.scrollHeight;
}

// 添加加载消息
function addLoadingMessage() {
    const chatHistory = document.getElementById('chat-history');
    const loadingDiv = document.createElement('div');
    const loadingId = 'loading_' + Date.now();
    
    loadingDiv.id = loadingId;
    loadingDiv.className = 'message loading-message';
    loadingDiv.innerHTML = `
        <div class="message-content">
            <div class="spinner-border loading-spinner" role="status"></div>
            正在计算中...
        </div>
    `;
    
    chatHistory.appendChild(loadingDiv);
    chatHistory.scrollTop = chatHistory.scrollHeight;
    
    return loadingId;
}

// 移除加载消息
function removeLoadingMessage(loadingId) {
    const loadingElement = document.getElementById(loadingId);
    if (loadingElement) {
        loadingElement.remove();
    }
}

// 更新发送按钮状态
function updateSendButton(disabled) {
    const sendBtn = document.getElementById('send-btn');
    
    if (disabled) {
        sendBtn.disabled = true;
        sendBtn.innerHTML = '<div class="spinner-border spinner-border-sm me-1" role="status"></div>计算中';
    } else {
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<i class="fas fa-paper-plane me-1"></i>发送';
    }
}

// 清空聊天记录
function clearChat() {
    if (confirm('确定要清空所有对话记录吗？')) {
        const chatHistory = document.getElementById('chat-history');
        chatHistory.innerHTML = `
            <div class="message assistant-message">
                <div class="message-content">
                    <i class="fas fa-robot me-2"></i>
                    对话已清空。我是数学计算助手，请告诉我您想要计算什么！
                </div>
                <div class="message-time">
                    <small class="text-muted">系统消息</small>
                </div>
            </div>
        `;
        
        // 生成新的会话ID
        currentThreadId = generateThreadId();
        
        // 更新系统状态显示
        loadSystemStatus();
    }
}
