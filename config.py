# 配置文件
import os

# 模型配置
MODEL_CONFIG = {
    "model_name": "gpt-4o-mini",  # 或者使用其他模型
    "base_url": "https://api.openai.com/v1",  # 或者使用其他API端点
    "api_key": os.getenv("OPENAI_API_KEY", "your-api-key-here"),
    "temperature": 0.1
}

# LangSmith 配置（可选）
LANGSMITH_CONFIG = {
    "tracing_v2": "true",
    "api_key": os.getenv("LANGCHAIN_API_KEY", ""),
    "endpoint": "https://api.smith.langchain.com",
    "project": "math-calculator-system"
}

# MCP 服务器配置
MCP_SERVERS = {
    'math_calculator': {
        'command': 'python',
        'args': ['math_mcp_server.py'],
        'cwd': '.',
        'transport': 'stdio'
    }
}

# FastAPI 配置
FASTAPI_CONFIG = {
    "host": "127.0.0.1",
    "port": 8000,
    "reload": True
}
