# 数学计算系统测试文件
import asyncio
import pytest
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from math_agent import MathCalculatorAgent

class TestMathCalculatorSystem:
    """数学计算系统测试类"""
    
    @pytest.fixture
    async def agent(self):
        """创建测试用的数学代理"""
        return MathCalculatorAgent()
    
    @pytest.mark.asyncio
    async def test_basic_addition(self, agent):
        """测试基础加法运算"""
        result = await agent.calculate("计算 15 + 27", "test_add")
        assert result["success"] == True
        assert "42" in result["result"]
    
    @pytest.mark.asyncio
    async def test_basic_subtraction(self, agent):
        """测试基础减法运算"""
        result = await agent.calculate("计算 100 - 45", "test_sub")
        assert result["success"] == True
        assert "55" in result["result"]
    
    @pytest.mark.asyncio
    async def test_basic_multiplication(self, agent):
        """测试基础乘法运算"""
        result = await agent.calculate("计算 8 × 9", "test_mul")
        assert result["success"] == True
        assert "72" in result["result"]
    
    @pytest.mark.asyncio
    async def test_basic_division(self, agent):
        """测试基础除法运算"""
        result = await agent.calculate("计算 144 ÷ 12", "test_div")
        assert result["success"] == True
        assert "12" in result["result"]
    
    @pytest.mark.asyncio
    async def test_power_operation(self, agent):
        """测试幂运算"""
        result = await agent.calculate("计算 2 的 8 次方", "test_power")
        assert result["success"] == True
        assert "256" in result["result"]
    
    @pytest.mark.asyncio
    async def test_square_root(self, agent):
        """测试平方根运算"""
        result = await agent.calculate("求 144 的平方根", "test_sqrt")
        assert result["success"] == True
        assert "12" in result["result"]
    
    @pytest.mark.asyncio
    async def test_factorial(self, agent):
        """测试阶乘运算"""
        result = await agent.calculate("计算 5 的阶乘", "test_factorial")
        assert result["success"] == True
        assert "120" in result["result"]
    
    @pytest.mark.asyncio
    async def test_percentage(self, agent):
        """测试百分比计算"""
        result = await agent.calculate("计算 25 占 200 的百分比", "test_percentage")
        assert result["success"] == True
        assert "12.5" in result["result"]
    
    @pytest.mark.asyncio
    async def test_linear_equation(self, agent):
        """测试一元一次方程求解"""
        result = await agent.calculate("解方程 3x + 6 = 0", "test_equation")
        assert result["success"] == True
        assert "-2" in result["result"]
    
    @pytest.mark.asyncio
    async def test_complex_expression(self, agent):
        """测试复杂表达式计算"""
        result = await agent.calculate("计算 (2 + 3) × 4 - 1", "test_expression")
        assert result["success"] == True
        assert "19" in result["result"]

def test_mcp_server_functions():
    """测试MCP服务器函数"""
    # 导入MCP服务器模块
    import math_mcp_server
    
    # 测试加法
    assert math_mcp_server.add(15, 27) == 42
    
    # 测试减法
    assert math_mcp_server.subtract(100, 45) == 55
    
    # 测试乘法
    assert math_mcp_server.multiply(8, 9) == 72
    
    # 测试除法
    assert math_mcp_server.divide(144, 12) == 12
    
    # 测试幂运算
    assert math_mcp_server.power(2, 8) == 256
    
    # 测试平方根
    assert math_mcp_server.square_root(144) == 12
    
    # 测试阶乘
    assert math_mcp_server.factorial(5) == 120
    
    # 测试百分比
    assert math_mcp_server.percentage(25, 200) == 12.5
    
    # 测试一元一次方程
    assert math_mcp_server.solve_linear_equation(3, 6) == -2
    
    # 测试表达式计算
    assert math_mcp_server.calculate_expression("(2 + 3) * 4 - 1") == 19

def test_error_handling():
    """测试错误处理"""
    import math_mcp_server
    
    # 测试除零错误
    try:
        math_mcp_server.divide(10, 0)
        assert False, "应该抛出除零异常"
    except ValueError as e:
        assert "除数不能为0" in str(e)
    
    # 测试负数平方根错误
    try:
        math_mcp_server.square_root(-1)
        assert False, "应该抛出负数平方根异常"
    except ValueError as e:
        assert "不能计算负数的平方根" in str(e)
    
    # 测试负数阶乘错误
    try:
        math_mcp_server.factorial(-1)
        assert False, "应该抛出负数阶乘异常"
    except ValueError as e:
        assert "不能计算负数的阶乘" in str(e)
    
    # 测试无效表达式错误
    try:
        math_mcp_server.calculate_expression("invalid expression")
        assert False, "应该抛出无效表达式异常"
    except ValueError as e:
        assert "无效的数学表达式" in str(e)

async def run_integration_tests():
    """运行集成测试"""
    print("开始运行数学计算系统集成测试...")
    
    # 创建代理实例
    agent = MathCalculatorAgent()
    
    # 测试用例
    test_cases = [
        ("计算 15 + 27", "42"),
        ("计算 100 - 45", "55"),
        ("计算 8 × 9", "72"),
        ("计算 144 ÷ 12", "12"),
        ("计算 2 的 8 次方", "256"),
        ("求 144 的平方根", "12"),
        ("计算 5 的阶乘", "120"),
        ("计算 25 占 200 的百分比", "12.5"),
        ("解方程 3x + 6 = 0", "-2"),
        ("计算 (2 + 3) × 4 - 1", "19")
    ]
    
    passed = 0
    failed = 0
    
    for i, (question, expected) in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {question}")
        try:
            result = await agent.calculate(question, f"integration_test_{i}")
            
            if result["success"] and expected in result["result"]:
                print(f"✅ 通过: {result['result']}")
                passed += 1
            else:
                print(f"❌ 失败: {result.get('result', result.get('error'))}")
                failed += 1
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
            failed += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}")
    print(f"失败: {failed}")
    print(f"总计: {passed + failed}")
    print(f"成功率: {passed / (passed + failed) * 100:.1f}%")
    
    return passed, failed

if __name__ == "__main__":
    # 运行基础函数测试
    print("=== 运行MCP服务器函数测试 ===")
    try:
        test_mcp_server_functions()
        print("✅ MCP服务器函数测试通过")
    except Exception as e:
        print(f"❌ MCP服务器函数测试失败: {e}")
    
    # 运行错误处理测试
    print("\n=== 运行错误处理测试 ===")
    try:
        test_error_handling()
        print("✅ 错误处理测试通过")
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
    
    # 运行集成测试
    print("\n=== 运行集成测试 ===")
    try:
        passed, failed = asyncio.run(run_integration_tests())
        if failed == 0:
            print("\n🎉 所有集成测试通过！")
        else:
            print(f"\n⚠️  有 {failed} 个测试失败")
    except Exception as e:
        print(f"❌ 集成测试运行失败: {e}")
