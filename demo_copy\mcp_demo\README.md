开发一个 MCP demo
步骤：
1. 安装uv包：pip install uv
2. 初始化项目：uv init mcp_demo
3. cd mcp_demo，可以看到新建的项目中已经有一些默认文件了
4. 创建并激活客户端虚拟环境：
   uv venv
   source .venv/bin/activate # linux下激活方式
   .venv\Scripts\activate # windows下激活方式
5. 编写MCP Server
   uv add mcp # 添加mcp依赖包，用于mcp服务端快速创建
   新建 server.py，编写代码（见server.py）
6. MCP服务端测试调试工具：[MCP Inspector](https://github.com/modelcontextprotocol/inspector)
   0）作用：快速调试MCP服务端，而无需通过client经过漫长的 prompt -> 调用 -> 返回
   1）需要安装node.js
   2）安装mcp inspector：npm install -g @modelcontextprotocol/inspector
   3）执行：mcp-inspector
   4）在页面上选填：transport type（如STDIO），command（如python），Arguments（如server.py）；将终端打印的Session token填入 Configuration 的Proxy Session Token输入框中；点击Connect即可。
   5）点击Tools、List Tools，可以看到列出server.py下所有tools（add）及其注释
   6）点击add这个tool，在右侧输入该方法的参数，点击“Run Tool”就能看到结果
7. 发布为一个即插即用MCP工具
   1）安装pyinstaller：python -m pip install pyinstaller  或  uv pip install pyinstaller
   2）打包：pyinstaller --onefile server.py
   3）打包后的可执行文件在 dist/server （二进制）
   4）使用（以qwen-agent为例）：
      function_list = [{
         'mcpServers': {
            'mcp_demo': {
               'command': '../mcp_demo/dist/server',
               'args': [],
               'cwd': '../mcp_demo' # 可选但推荐，工作目录，指向代码所在目录
            }
         }
      }]
      这种方式优点：环境无关，缺点：每次修改都要重新打包，替代方案（指定虚拟环境python路径）：
      {
         'mcp_demo': {
            'command': '../mcp_demo/.venv/bin/python',
            'args': ['server.py'],
            'cwd': '../mcp_demo'
         }
      }
   5）注：除了用pyinstaller把代码打包成可执行文件外，还可以发布为pip包并全局安装（如果工具是python包，发布到PyPI或私有源，然后用pip install mcp_demo安装）、用Docker容器（把mcp_demo做成Docker镜像）